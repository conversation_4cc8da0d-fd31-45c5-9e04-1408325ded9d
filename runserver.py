import os
from argparse import ArgumentParser

from finance_erp.wsgi import app


def main():
    parser = ArgumentParser()

    parser.add_argument("-i", '--host',
                        help="Host url, e.g.: example.com, 127.0.0.1",
                        default='127.0.0.1',
                        nargs='?')

    parser.add_argument("-p", "--port",
                        help="port, e.g.:5000",
                        default='9002',
                        nargs='?')

    args = parser.parse_args()

    os.environ['FLASK_APP'] = app.name
    os.environ['FLASK_ENV'] = 'local'
    app.run(host=args.host, port=args.port)


if __name__ == '__main__':
    main()
