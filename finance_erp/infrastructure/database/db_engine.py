import logging

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session, sessionmaker
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.exceptions import InvalidTenantException
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

logger = logging.getLogger(__name__)

Base = declarative_base()

tenant_wise_sessions = dict()
tenant_wise_engines = dict()


class DBCreds(object):
    def __init__(self, db_uri, db_schema, username, password, host, port, dbname):
        self.db_uri = db_uri
        self.db_schema = db_schema
        self.username = username
        self.password = password
        self.host = host
        self.port = port
        self.dbname = dbname


def get_database_uris(tenant_id=None):
    database_uris = dict()
    active_tenants = TenantClient.get_active_tenants()
    if tenant_id:
        active_tenants = [
            tenant for tenant in active_tenants if tenant.tenant_id == tenant_id
        ]

    for tenant in active_tenants:
        tenant_id = tenant.tenant_id
        try:
            dbcreds = AwsSecretManager.get_db_credentials(tenant_id)
            tenant_db_url = "postgresql://{0}:{1}@{2}:{3}/{4}".format(
                dbcreds["username"],
                dbcreds["password"],
                dbcreds["host"],
                dbcreds["port"],
                dbcreds["dbname"],
            )
            database_uris[tenant_id] = DBCreds(
                tenant_db_url,
                dbcreds.get("schema_name"),
                dbcreds["username"],
                dbcreds["password"],
                dbcreds["host"],
                dbcreds["port"],
                dbcreds["dbname"],
            )
        except Exception as e:
            logger.exception(
                "Can't retrieve database credentials for tenant_id: %s, Skipping",
                tenant_id,
            )

    return database_uris


def add_session_for_tenant(db_creds, tenant_id):
    if db_creds.db_schema:
        engine = create_engine(
            db_creds.db_uri,
            execution_options={"schema_translate_map": {None: db_creds.db_schema}},
        )
    else:
        engine = create_engine(db_creds.db_uri)
    tenant_wise_engines[tenant_id] = engine
    tenant_wise_sessions[tenant_id] = scoped_session(sessionmaker(bind=engine))


def setup_tenant_sessions(tenant_id=None):
    """
    Setup a registry of sessions for each tenant database connections (Using scoped_session). The setup should be
    done only once, on application startup. So this method is called on at module level.

    To access tenant database, get the session for that specific tenant from the created tenant_wise_session,
    and execute query against that session.

    The value returned by scoped_session is a thread-local session, created using sessionmaker, which is the session
    factory used to create session in general

    :return:
    """
    database_uris = get_database_uris(tenant_id=tenant_id)
    if tenant_id:
        database_uri = database_uris.get(tenant_id)
        if not database_uri:
            raise InvalidTenantException()

        add_session_for_tenant(database_uri, tenant_id)
    else:
        for tenant_id, database_uri in database_uris.items():
            if tenant_id in tenant_wise_engines:
                continue
            add_session_for_tenant(database_uri, tenant_id)


def get_scoped_session(tenant_id=None):
    """
    :param tenant_id:
    """
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    tenant_scoped_session = tenant_wise_sessions.get(tenant_id)

    if not tenant_scoped_session:
        setup_tenant_sessions(tenant_id)
        tenant_scoped_session = tenant_wise_sessions.get(tenant_id)

    return tenant_scoped_session


def get_session(tenant_id=None):
    return get_scoped_session(tenant_id=tenant_id)


def get_engine(tenant_id=None):
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    engine = tenant_wise_engines.get(tenant_id)

    if not engine:
        setup_tenant_sessions(tenant_id)
        engine = tenant_wise_engines.get(tenant_id)

    return engine


def remove_session(tenant_id=None):
    """
    The scoped_session.remove() method first calls Session.close() on the current Session, if present, which has the
    effect of releasing any connection/transactional resources owned by the Session first, then discarding the
    Session itself. "Releasing" here means that connections are returned to their connection pool and any
    transactional state is rolled back, ultimately using the rollback() method of the underlying DBAPI connection.

    :param tenant_id:
    :return:
    """
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    tenant_scoped_session = tenant_wise_sessions.get(tenant_id)

    if tenant_scoped_session:
        tenant_scoped_session.remove()
