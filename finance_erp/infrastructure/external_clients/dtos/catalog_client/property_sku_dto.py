class PropertySkuDTO(object):
    def __init__(self, expense_item_id, name, sku_category):
        self.expense_item_id = expense_item_id
        self.name = name
        self.sku_category = sku_category

    @staticmethod
    def create_from_catalog_data(sku_data):
        return PropertySkuDTO(
            expense_item_id=sku_data.get("code"),
            name=sku_data.get("name"),
            sku_category=sku_data.get("sku_category_code"),
        )
