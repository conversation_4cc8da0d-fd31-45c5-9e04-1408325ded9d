class SellerDTO(object):
    def __init__(self, legal_name, name, seller_id, seller_category_id):
        self.legal_name = legal_name
        self.name = name
        self.seller_id = seller_id
        self.seller_category_id = seller_category_id

    @staticmethod
    def create_from_catalog_seller_data(seller_data):
        return SellerDTO(
            legal_name=seller_data.get("legal_name"),
            name=seller_data.get("name"),
            seller_id=seller_data.get("seller_id"),
            seller_category_id=seller_data.get("seller_category_id"),
        )
