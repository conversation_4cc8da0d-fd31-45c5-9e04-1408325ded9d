class TaxConfigDTO(object):
    def __init__(self, tax_code, sku_category, tax_type, tax_value):
        self.tax_code = tax_code
        self.sku_category = sku_category
        self.tax_type = tax_type
        self.tax_value = tax_value

    @staticmethod
    def create_from_tax_service_data(tax_config_data):
        return TaxConfigDTO(
            tax_code=tax_config_data.get("tax_code"),
            sku_category=tax_config_data.get("service_category"),
            tax_type=tax_config_data.get("tax_type"),
            tax_value=tax_config_data.get("tax_value"),
        )
