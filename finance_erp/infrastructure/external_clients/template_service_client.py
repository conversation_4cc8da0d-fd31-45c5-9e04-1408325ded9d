import logging
from enum import Enum

from ths_common.exceptions import DownstreamSystemFailure
from ths_common.utils.id_generator_utils import random_id_generator

from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


class TemplateFormat(Enum):
    PDF = "pdf"
    HTML = "html"


class TemplateNameSpace(Enum):
    STAY_SUMMARY = "b2b_stay_summary"


@register_instance()
class TemplateServiceClient(BaseExternalClient):
    page_map = {
        "generate": dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/v1/generate"
        ),
        "merge_pdfs": dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/merge-pdfs"
        ),
    }

    def __init__(self):
        super().__init__(timeout=300)

    def get_domain(self):
        return ServiceRegistryClient.get_template_service_url()

    def generate(
        self,
        namespace: str,
        context: dict,
        template_format: TemplateFormat = TemplateFormat.PDF,
    ):
        s3_file_path = "{}/{}".format(namespace, random_id_generator(namespace[:10]))
        logger.debug(
            "Generate template for namespace: %s of type: %s upload: %s context: %s",
            namespace,
            template_format.value,
            s3_file_path,
            context,
        )
        request_dict = dict(
            template=namespace,
            context=context,
            format=template_format.value,
            s3FilePath=s3_file_path,
        )

        response = self.make_call("generate", request_dict)

        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to upload template.",
                description="Failed to upload template with code: {0}".format(
                    response.response_code
                ),
                extra_payload=None,
            )

        link = response.json_response.get("link")
        if not link:
            logger.error(
                "No link from template service. Response: {}".format(
                    response.response.text
                )
            )

        return link

    def merge_pdfs(
        self,
        resource_id,
        pdf_links,
        merged_file_name,
    ):
        logger.debug(
            "Pdf merging for resource_id: %s",
            resource_id,
        )
        request_dict = dict(
            booking_id=resource_id,
            pdfs=pdf_links,
            mergedPdfName=merged_file_name,
        )

        response = self.make_call("merge_pdfs", request_dict)

        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to upload template.",
                description="Failed to merge pdfs code: {0}".format(
                    response.response_code
                ),
                extra_payload=None,
            )

        link = response.json_response.get("link")
        if not link:
            logger.error(
                "No link from template service. Response: {}".format(
                    response.response.text
                )
            )

        return link
