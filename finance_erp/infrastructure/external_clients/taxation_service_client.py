from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.dtos.taxation_client.tax_config_dto import (
    TaxConfigDTO,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class TaxationServiceClient(BaseExternalClient):
    page_map = {
        "get_tax_configs": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/tax/v5/{property_id}/tax_configs",
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_tax_service_url()

    def get_tax_configs(self, property_id):
        page_name = "get_tax_configs"
        url_params = dict(property_id=property_id)
        response = self.make_call(
            page_name,
            url_parameters=url_params,
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            TaxConfigDTO.create_from_tax_service_data(sku)
            for sku in response.json_response["tax_configs"]
        ]
