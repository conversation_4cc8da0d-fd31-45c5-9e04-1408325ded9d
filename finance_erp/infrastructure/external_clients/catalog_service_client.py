import json
from typing import List

from finance_erp.common.constants import (
    BUSINESS_CENTRAL_ENDPOINT_CONFIG,
    FINANCE_ERP_METABASE_HOME_DASHBOARD_ID,
    TRANSACTION_MASTER_SUB_CODE_DETAILS,
)
from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.dtos.catalog_client.property_sku_dto import (
    PropertySkuDTO,
)
from finance_erp.infrastructure.external_clients.dtos.catalog_client.seller_dto import (
    SellerDTO,
)
from finance_erp.infrastructure.external_clients.dtos.catalog_client.seller_sku_dto import (
    SellerSkuDTO,
)
from finance_erp.infrastructure.external_clients.dtos.catalog_client.tenant_config_dto import (
    TenantConfigDto,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class CatalogServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=3000)

    page_map = {
        "get_hotels": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v2/properties/",
        ),
        "get_tenant_configs": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/tenant-configs",
        ),
        "get_enums": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/enums",
        ),
        "get_skus": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/properties/{property_id}/sku/",
        ),
        "get_seller_skus": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/sellers/{seller_id}/skus",
        ),
        "get_property_seller_details": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/sellers?property_id={property_id}",
        ),
    }

    def get_domain(self):
        return "https://catalog.private-62de1bdb96538000013f7adf.treebo.facets.cloud"
        return ServiceRegistryClient.get_catalog_service_url()

    def get_hotels(self, request_data, fields, status=None):
        page_name = "get_hotels"
        property_id = request_data.get("hotel_ids")
        data = dict(fields=fields, status=status)
        if property_id:
            data.update(property_id=property_id)
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def get_tenant_config(self, config_key, property_id):
        tenant_configs = self.get_tenant_configs(property_id)
        tenant_config_dict = {config.config_name: config for config in tenant_configs}
        return tenant_config_dict.get(config_key)

    def get_metabase_dashboard_id(self, property_id=None):
        dashboard_id = self.get_tenant_config(
            config_key=FINANCE_ERP_METABASE_HOME_DASHBOARD_ID, property_id=property_id
        )
        if dashboard_id:
            return int(dashboard_id.config_value)
        return None

    def get_tenant_configs(self, property_id=None) -> List[TenantConfigDto]:
        page_name = "get_tenant_configs"
        optional_url_params = dict(property_id=property_id) if property_id else None
        response = self.make_call(
            page_name=page_name, optional_url_params=optional_url_params
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            TenantConfigDto(
                config.get("config_name"),
                config.get("config_value"),
                config.get("value_type"),
            )
            for config in response.json_response
        ]

    def get_enums(self, property_id=None, enum_names=None):
        page_name = "get_enums"
        optional_url_params = dict()
        if property_id:
            optional_url_params["property_id"] = property_id
        if enum_names:
            if isinstance(enum_names, list):
                enum_names = ",".join(enum_names)
            optional_url_params["enum_names"] = enum_names
        response = self.make_call(
            page_name=page_name, optional_url_params=optional_url_params
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.response.content
                )
            )
        enums = response.json_response
        enums_list = []
        for enum in enums:
            enum_values = [
                enum_value.get("value") for enum_value in enum.get("enum_values")
            ]
            enums_list.append(
                dict(enum_name=enum["enum_name"], enum_values=enum_values)
            )
        return enums_list

    def get_property_skus(self, property_id, for_inclusions=False, codes=None):
        page_name = "get_skus"
        url_params = dict(property_id=property_id)
        response = self.make_call(
            page_name,
            url_parameters=url_params,
            optional_url_params=dict(for_inclusions=for_inclusions, codes=codes),
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            PropertySkuDTO.create_from_catalog_data(sku)
            for sku in response.json_response
        ]

    def get_seller_skus(self, seller_id):
        page_name = "get_seller_skus"
        url_params = dict(seller_id=seller_id)
        response = self.make_call(
            page_name,
            url_parameters=url_params,
        )
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            SellerSkuDTO.create_from_catalog_seller_sku_data(sku)
            for sku in response.json_response["skus"]
        ]

    def fetch_seller(self, property_id):
        page_name = "get_property_seller_details"
        url_params = dict(property_id=property_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            SellerDTO.create_from_catalog_seller_data(seller)
            for seller in response.json_response["sellers"]
        ]

    def get_business_central_endpoint_configs(self, property_id=None):
        config = self.get_tenant_config(
            config_key=BUSINESS_CENTRAL_ENDPOINT_CONFIG, property_id=property_id
        )
        if not config:
            raise Exception("Business Central Config not found in catalog")
        return json.loads(config.config_value)

    def get_transaction_master_sub_code_details(self, property_id=None):
        config = self.get_tenant_config(
            config_key=TRANSACTION_MASTER_SUB_CODE_DETAILS, property_id=property_id
        )
        if not config:
            raise Exception("Business Central Config not found in catalog")
        return json.loads(config.config_value)
