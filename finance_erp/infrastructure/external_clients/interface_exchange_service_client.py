from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.core.constants import InterfaceTypes
from finance_erp.infrastructure.external_clients.dtos.interface_exchange_client.interface_dtos import (
    InterfaceDTO,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class InterfaceExchangeServiceClient(BaseExternalClient):
    page_map = {
        "get_interfaces": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/interface-exchange/v1/interfaces",
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_interface_exchange_service_url()

    def get_interfaces(
        self, property_id, interface_type=InterfaceTypes.POS, include_mappings=False
    ):
        page_name = "get_interfaces"
        response = self.make_call(
            page_name,
            optional_url_params=dict(
                property_id=property_id,
                interface_type=interface_type,
                include_mappings=include_mappings,
            ),
        )
        if not response.is_success():
            raise Exception(
                "Interface Exchange API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            InterfaceDTO.create_from_interfaces_data(interface)
            for interface in response.json_response["data"].get("interfaces", [])
        ]
