import logging

from ths_common.exceptions import DownstreamSystemFailure

from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class RoleManagerClient(BaseExternalClient):
    page_map = {
        "get_privilege": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/role-manager/roles/{role_name}/privileges",
        )
    }

    def __init__(self):
        super().__init__()

    def get_domain(self):
        return ServiceRegistryClient.get_role_manager_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        return headers

    def get_privilege_by_role_name(self, role_name):
        page_name = "get_privilege"
        url_params = dict(role_name=role_name)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            logger.error("Role Manager API failed: %s", response)
            raise DownstreamSystemFailure(
                message="Role Privilege API Error",
                description="Role Privilege API Error Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                ),
                extra_payload=url_params,
            )
        api_response = None
        try:
            api_response = (
                [RolePrivilegesDTO.from_json(r) for r in response.data]
                if response.data
                else []
            )
        except Exception as e:
            error = f"Generic Exception in get role privilege call: {str(e)}"
            logger.exception(error)

        return api_response
