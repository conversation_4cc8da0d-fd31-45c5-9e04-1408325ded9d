import datetime


def create_hotel_payload():
    return [
        {
            "LegalName": "Vendor Company",
            "Name": "Vendor",
            "Address": "Vendor Address",
            "City": "Vendor City",
            "CountryCode": "Vendor Country Code",
            "PANNo": "Vendor PAN",
            "State": "Vendor State Code",
            "GSTRegistrationNo": "Vendor GSTIN",
            "GSTVendorType": "Registered",
            "HotelCode": "123",
            "MSME": "Vendor MSME",
            "CostCenterID": "TRB-S-BHA-123",
        }
    ]


def get_hotel_payload():
    return {"hotel_codes": "123"}


def update_hotel_payload():
    return [{"verified": False, "HotelCode": "123"}]


def bulk_push_hotel_payload():
    return {"HotelCode": ["123"]}


def create_corporate_payload():
    return [
        {
            "customer_legal_name": "ABC Company",
            "customer_trading_name": "ABC Corp",
            "address": "123 Main Street",
            "city": "Cityville",
            "phone_number": "************",
            "post_code": "12345",
            "email": "<EMAIL>",
            "credit_limit": "100000",
            "country_code": "US",
            "pan": "**********",
            "state_code": "ST",
            "gstin": "GSTIN12345",
            "gst_customer_type": "REGISTERED",
            "corporate_code": "ATH123",
            "tan_number": "TAN12345",
        }
    ]


def get_corporate_payload():
    return {"corporate_codes": "ATH123"}


def update_corporate_payload():
    return [{"verified": False, "corporate_code": "ATH123"}]


def bulk_push_corporate_payload():
    return {"corporate_codes": ["ATH123"]}


def create_ota_payload():
    checkin = datetime.datetime.now().strftime("%Y-%m-%d")
    checkout = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime(
        "%Y-%m-%d"
    )

    return [
        {
            "PostingDate": checkin,
            "GBV": "1000",
            "CommissionAmount": "100",
            "TDSPer": "10",
            "BookingID": "ABC123",
            "Hotelcode": "123",
            "CheckInDate": checkin,
            "CheckOutDate": checkout,
            "NBV": "900",
            "GuestName": "John Doe",
            "OTAName": "OTA",
            "CommissionPer": "5",
            "MOP": "Credit Card",
            "BookingCreationDate": checkin,
        }
    ]


def get_ota_payload():
    return {"reference_number": "ABC123"}


def update_ota_payload():
    return [{"verified": False, "reference_number": "ABC123"}]


def bulk_push_ota_payload():
    return {"reference_numbers": ["ABC123"]}


def create_payment_payload():
    return [
        {
            "HotelCode": "123",
            "PostingDate": "2022-05-24",
            "PaymentAmount": "10000.00",
            "PaymentGatewayTransactionId": "test_JZ71c2fLpH5EGk",
            "BookingID": "TEST-70222404650996",
            "PGCharges": "8.00",
            "PGTaxes": "1.44",
            "PlatformFees": "0.00",
            "PaymentDate": "2023-07-20",
            "PaidBy": "guest",
            "PaidTo": "treebo",
            "PaymentType": "ptt",
            "Paymode": "razorpay_api",
            "PaymodeType": "VISA",
            "PayorEntity": "corptest",
            "BookerEntity": "corptest",
            "BookingOwner": {
                "name": "test",
                "phone": "9898989898",
                "email": "<EMAIL>",
            },
            "AthenaCode": "corptest",
            "PayorName": "TEST COMPANY",
            "HotelName": "Itsy Hotels by Treebo - Test-Hotel",
            "InvoiceId": "INV-010623-0454-3045-3457",
            "CheckInDate": "2023-07-24",
            "CheckOutDate": "2023-07-26",
            "Channel": "ota",
            "SubChannel": "booking-com",
            "OriginalBookingAmount": "10000.00",
            "IsAdvance": False,
            "UUID": "BIL-092323-1647-4022-2430-1-1/TRB-46750317276",
            "SellerModel": "RESELLER",
            "RefundReason": "Cancellation - Overbooking",
        }
    ]


def get_payment_payload():
    return {"uu_ids": "BIL-092323-1647-4022-2430-1-1/TRB-46750317276"}


def update_payment_payload():
    return [
        {"verified": False, "uu_id": "BIL-092323-1647-4022-2430-1-1/TRB-46750317276"}
    ]


def bulk_push_payment_payload(uu_id):
    return {"uu_ids": [uu_id]}


def create_purchase_payload():
    checkin = datetime.datetime.now().strftime("%Y-%m-%d")
    checkout = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime(
        "%Y-%m-%d"
    )

    return [
        {
            "EntryType": "Credit",
            "OrderDate": checkin,
            "PostingDate": checkin,
            "BookingRefNo": "ABC123",
            "State": "ABC",
            "Structure": "GST",
            "NatureofSupply": "B2B",
            "UnitPrice": "100",
            "GSTGroupCode": "18",
            "HotelName": "Example Hotel",
            "Check-In": checkin,
            "Check-Out": checkout,
            "TotalDays": "2",
            "RoomType": "Deluxe",
            "NoofPax": "2",
            "GuestName": "John Doe",
            "UVIDDate": checkin,
            "UVIDNo": "INV123",
            "UVAmount": "1000",
            "Hotelcode": "123",
            "TransactionRefId": "REF123",
            "Source": "Website",
            "SubSource": "Booking Page",
            "VendorNo": "VEND123",
            "DueDate": checkout,
            "GSTVendorType": "Registered",
            "Remark": "Example remark",
            "HSNSACCode": "123456",
            "OriginalInvNo": "INV456",
            "SourceCreatedOn": checkin,
            "CustomerInvoiceNumber": "CINV123",
        }
    ]


def get_purchase_payload():
    return {"unique_ref_ids": "REF123"}


def update_purchase_payload():
    return [{"verified": False, "unique_ref_id": "REF123"}]


def bulk_push_purchase_payload():
    return {"unique_ref_ids": ["REF123"]}


def create_sales_payload():
    checkin = datetime.datetime.now().strftime("%Y-%m-%d")
    checkout = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime(
        "%Y-%m-%d"
    )

    return [
        {
            "EntryType": "Entry Type Value",
            "OrderDate": checkin,
            "PostingDate": checkin,
            "BookingRefNo": "Booking Reference Number",
            "State": "State Code",
            "Structure": "GST Structure Value",
            "NatureofSupply": "Nature of Supply Value",
            "UnitPrice": "23",
            "GSTGroupCode": "12",
            "HotelName": "Hotel Name",
            "Check-In": checkin,
            "Check-Out": checkout,
            "TotalDays": "Total Stay Days",
            "RoomType": "Room Type",
            "NoofPax": "Occupancy",
            "GuestName": "Guest Name",
            "UVIDDate": checkin,
            "UVIDNo": "UVID Number",
            "UVAmount": "10000",
            "Hotelcode": "Hotel Code",
            "TransactionRefId": "REF123",
            "Source": "Source Value",
            "SubSource": "Sub Source Value",
            "CustomerNo": "Customer Number",
            "GSTCustomerType": "GST Customer Type",
            "GSTBilltoStateCode": "Billed To State Code",
            "CustGSTRegNo": "Billed To GSTIN",
            "Remarks": "Remarks",
            "CustomerName": "Billed To Legal Name",
            "HSN_SACCode": "HSN Code",
            "OriginalInvoiceNo": "Original Invoice Number",
            "Type": "Invoice Charge Type",
            "BuySideInvoiceNumber": "Buy Side Invoice Number",
            "BookingOwnerLegalEntityId": "1",
        }
    ]


def create_sales_payload_in_bulk():
    checkin = datetime.datetime.now().strftime("%Y-%m-%d")
    checkout = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime(
        "%Y-%m-%d"
    )

    return [
        {
            "EntryType": "Entry Type Value",
            "OrderDate": checkin,
            "PostingDate": checkin,
            "BookingRefNo": "Booking Reference Number",
            "State": "State Code",
            "Structure": "GST Structure Value",
            "NatureofSupply": "Nature of Supply Value",
            "UnitPrice": f"23{i}",
            "GSTGroupCode": "12",
            "HotelName": "Hotel Name",
            "Check-In": checkin,
            "Check-Out": checkout,
            "TotalDays": "Total Stay Days",
            "RoomType": "Room Type",
            "NoofPax": "Occupancy",
            "GuestName": "Guest Name",
            "UVIDDate": checkin,
            "UVIDNo": "UVID Number",
            "UVAmount": "121",
            "Hotelcode": "Hotel Code",
            "TransactionRefId": f"REF123{i}",
            "Source": "Source Value",
            "SubSource": "Sub Source Value",
            "CustomerNo": "Customer Number",
            "GSTCustomerType": "GST Customer Type",
            "GSTBilltoStateCode": "Billed To State Code",
            "CustGSTRegNo": "Billed To GSTIN",
            "Remarks": "Remarks",
            "CustomerName": "Billed To Legal Name",
            "HSN_SACCode": "HSN Code",
            "OriginalInvoiceNo": f"Original Invoice Number{i}",
            "Type": "Invoice Charge Type",
            "BuySideInvoiceNumber": f"Buy Side Invoice Number{i}",
            "BookingOwnerLegalEntityId": "1",
        }
        for i in range(0, 5)
    ]


def get_sales_payload():
    return {"unique_ref_ids": "REF123"}


def update_sales_payload(unique_ref_id=None):
    return [{"verified": False, "unique_ref_id": unique_ref_id or "REF123"}]


def bulk_push_sales_payload():
    return {"unique_ref_ids": ["REF123"]}


def create_expense_settlement_payload():
    return [
        {
            "PostingDate": "2023-06-14",
            "PTaxAmount": "100",
            "TDSPer": "12",
            "HotelCode": "123",
            "VendorInvoiceNo": "12345",
            "InvoiceAmount": "112",
            "InvoiceDate": "2023-06-14",
            "EntryType": "990",
            "HSNSACCode": "Hello",
            "DocType": "Document Type Value",
            "Remarks": "Remarks Value",
        }
    ]


def get_expense_settlement_payload():
    return {"uu_ids": "990/123/2023-06-14"}


def update_expense_settlement_payload():
    return [{"verified": False, "uu_id": "990/123/2023-06-14"}]


def bulk_push_expense_settlement_payload():
    return {"uu_ids": ["990/123/2023-06-14"]}


def create_hotel_adjustment_payload():
    return [
        {
            "HotelCode": "ABC123",
            "PostingDate": "2023-06-14",
            "JVAmount": "1000.00",
            "InvoiceNo": "INV-123",
            "InvoiceDate": "2023-06-14",
            "InvoiceAmount": "800.00",
            "Remarks": "Adjustment for extra services",
            "AdjustmentType": "Credit",
            "EntryType": "Journal",
            "DocType": "Adjustment",
        }
    ]


def get_hotel_adjustment_payload():
    return {"uu_ids": "Journal/Credit/ABC123/2023-06-14"}


def update_hotel_adjustment_payload():
    return [{"verified": False, "uu_id": "Journal/Credit/ABC123/2023-06-14"}]


def bulk_push_hotel_adjustment_payload():
    return {"uu_ids": ["Journal/Credit/ABC123/2023-06-14"]}


def create_loan_settlement_payload():
    return [
        {
            "HotelCode": "HOTEL123",
            "PostingDate": "2023-06-14",
            "LoanAmount": "50000.00",
            "Remarks": "Loan for property renovation",
            "EntryType": "Loan",
            "DocType": "Agreement",
        }
    ]


def get_loan_settlement_payload():
    return {"uu_ids": "Loan/HOTEL123/2023-06-14"}


def update_loan_settlement_payload():
    return [{"verified": False, "uu_id": "Loan/HOTEL123/2023-06-14"}]


def bulk_push_loan_settlement_payload():
    return {"uu_ids": ["Loan/HOTEL123/2023-06-14"]}


def create_tax_settlement_payload():
    return [
        {
            "HotelCode": "HOTEL123",
            "PostingDate": "2023-06-14",
            "CGSTAmt": "100.00",
            "SGSTAmt": "100.00",
            "Amount": "200.00",
            "EntryType": "Sales",
            "DocType": "Invoice",
        }
    ]


def get_tax_settlement_payload():
    return {"uu_ids": "Sales/HOTEL123/2023-06-14"}


def update_tax_settlement_payload():
    return [{"verified": False, "uu_id": "Sales/HOTEL123/2023-06-14"}]


def bulk_push_tax_settlement_payload():
    return {"uu_ids": ["Sales/HOTEL123/2023-06-14"]}


def create_treebo_fee_settlement_payload():
    return [
        {
            "HotelCode": "HOTEL123",
            "SettlementDate": "2023-06-14",
            "Remarks": "Settlement payment",
            "Description": "Room charges",
            "UnitAmount": "100.00",
            "GSTPercent": "18",
            "EntryType": "Expense",
            "HSNSACCode": "1234",
            "DocType": "Payment",
        }
    ]


def get_treebo_fee_settlement_payload():
    return {"uu_ids": "Expense/HOTEL123/2023-06-14"}


def update_treebo_fee_settlement_payload():
    return [{"verified": False, "uu_id": "Expense/HOTEL123/2023-06-14"}]


def bulk_push_treebo_fee_settlement_payload():
    return {"uu_ids": ["Expense/HOTEL123/2023-06-14"]}


def create_pos_revenue_payload(bill_id="bill_001"):
    return {
        "interface_id": "1",
        "interface_name": "POS",
        "hotel_id": "hotel_001",
        "bill_id": bill_id,
        "reservation_id": "res_001",
        "guest_name": "John Doe",
        "amount": 100.0,
        "sku_id": "sku_001",
        "tax": 5.0,
        "tax_details": [
            {"tax_type": "sgst", "amount": 2.5, "percentage": 2.5},
            {"tax_type": "cgst", "amount": 2.5, "percentage": 2.5},
        ],
        "sku_category": "food",
        "hsn_code": "1234",
        "payment_method": "credit_card",
        "pos_bill_date": "2023-01-01",
        "pos_bill_time": "11:00:00",
        "revenue_center": "cafe",
        "serving_time": "breakfast",
        "workstation_id": "pos01",
        "waiter_id": "1",
    }
