from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_hotel,
    create_hotel,
    get_hotel,
    update_hotel,
)
from finance_erp.tests.payload_generator import (
    bulk_push_hotel_payload,
    create_hotel_payload,
    get_hotel_payload,
    update_hotel_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_hotel_api(client):
    create_hotel(client, create_hotel_payload())
    hotel_details = get_hotel(client, get_hotel_payload())
    assert hotel_details
    assert validate_fields_are_not_null(hotel_details)


def test_update_hotel_api(client):
    create_hotel(client, create_hotel_payload())
    update_hotel(client, update_hotel_payload())
    hotel_details = get_hotel(client, get_hotel_payload())
    assert hotel_details["verified"] is False


def test_bulk_push_hotel_api(client):
    create_hotel(client, create_hotel_payload())
    bulk_push_hotel(client, bulk_push_hotel_payload())
    hotel_details = get_hotel(client, get_hotel_payload())
    assert hotel_details["status"] == ERPEntityStatus.PUSHED
