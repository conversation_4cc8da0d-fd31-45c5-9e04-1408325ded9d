from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import bulk_push_ota, create_ota, get_ota, update_ota
from finance_erp.tests.payload_generator import (
    bulk_push_ota_payload,
    create_ota_payload,
    get_ota_payload,
    update_ota_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_ota_api(client):
    create_ota(client, create_ota_payload())
    ota_details = get_ota(client, get_ota_payload())
    assert ota_details
    assert validate_fields_are_not_null(ota_details)


def test_update_ota_api(client):
    create_ota(client, create_ota_payload())
    update_ota(client, update_ota_payload())
    ota_details = get_ota(client, get_ota_payload())
    assert ota_details["verified"] is False


def test_bulk_push_ota_api(client):
    create_ota(client, create_ota_payload())
    bulk_push_ota(client, bulk_push_ota_payload())
    ota_details = get_ota(client, get_ota_payload())
    assert ota_details["status"] == ERPEntityStatus.PUSHED
