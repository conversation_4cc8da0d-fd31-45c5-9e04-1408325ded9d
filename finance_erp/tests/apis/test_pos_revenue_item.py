from finance_erp.tests.mockers import create_pos_revenue_item, get_pos_revenue_items
from finance_erp.tests.payload_generator import create_pos_revenue_payload
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_create_pos_revenue_item(client):
    payload = [create_pos_revenue_payload()]
    response = create_pos_revenue_item(client, payload)
    create_response = response["data"]
    assert create_response
    for item in create_response:
        assert validate_fields_are_not_null(item)


def test_get_pos_revenue_items_positive_query(client):
    payload = [create_pos_revenue_payload(bill_id="test_12")]
    create_pos_revenue_item(client, payload)
    query = dict(bill_id="test_12")
    get_response = get_pos_revenue_items(client, query)
    get_response_data = get_response["data"]
    assert get_response_data
    for item in get_response_data:
        assert item["bill_id"] == "test_12"
        assert validate_fields_are_not_null(item)


def test_get_pos_revenue_items_negative_query_should_return_empty_response(client):
    payload = [create_pos_revenue_payload(bill_id="test_123")]
    create_pos_revenue_item(client, payload)
    query = dict(bill_id="missing_id")
    get_response = get_pos_revenue_items(client, query)
    get_response_data = get_response["data"]
    assert not get_response_data
