from finance_erp.domain.payment.models import PGPaymentStatus
from finance_erp.tests.mockers import (
    aggregate_payment,
    bulk_push_payment,
    create_hotel,
    create_payment,
    get_payment,
    update_payment,
)
from finance_erp.tests.payload_generator import (
    bulk_push_payment_payload,
    create_hotel_payload,
    create_payment_payload,
    get_payment_payload,
    update_payment_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_payment_api(client):
    create_payment(client, create_payment_payload())
    payment_details = get_payment(client, get_payment_payload())
    assert payment_details
    assert validate_fields_are_not_null(payment_details)


def test_update_payment_api(client):
    create_payment(client, create_payment_payload())
    update_payment(client, update_payment_payload())
    payment_details = get_payment(client, get_payment_payload())
    assert payment_details["verified"] is False


def test_payment_aggregates_gets_deleted_on_updating_aggregated_payments(
    client, pg_payment_summary_repo
):
    create_payment(client, create_payment_payload())
    aggregate_payment(client, {})
    update_payment(client, update_payment_payload())
    payment_details = get_payment(client, get_payment_payload())
    assert payment_details["aggregation_id"] in (None, "")
    assert payment_details["status"] == "ingested"
    payment_summary = pg_payment_summary_repo.get_by_pg_ids(
        [payment_details["PaymentGatewayTransactionId"]]
    )
    assert payment_summary[0].deleted == True


def test_bulk_push_payment_api(client):
    create_hotel(client, create_hotel_payload())
    create_payment(client, create_payment_payload())
    aggregate_payment(client, {})
    payment_details = get_payment(client, get_payment_payload())
    bulk_push_payment(
        client, dict(push_all=True, hotel_code=payment_details["HotelCode"])
    )
    payment_details = get_payment(client, get_payment_payload())
    assert payment_details["status"] == PGPaymentStatus.PUSHED
