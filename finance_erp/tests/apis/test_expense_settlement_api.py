from finance_erp.domain.base import ERPEntityStatus
from finance_erp.tests.mockers import (
    bulk_push_expense_settlement,
    create_expense_settlement,
    get_expense_settlement,
    update_expense_settlement,
)
from finance_erp.tests.payload_generator import (
    bulk_push_expense_settlement_payload,
    create_expense_settlement_payload,
    get_expense_settlement_payload,
    update_expense_settlement_payload,
)
from finance_erp.tests.validators.common_validators import validate_fields_are_not_null


def test_add_expense_settlement_api(client):
    create_expense_settlement(client, create_expense_settlement_payload())
    expense_details = get_expense_settlement(client, get_expense_settlement_payload())
    assert expense_details
    assert validate_fields_are_not_null(expense_details)


def test_update_expense_settlement_api(client):
    create_expense_settlement(client, create_expense_settlement_payload())
    update_expense_settlement(client, update_expense_settlement_payload())
    expense_details = get_expense_settlement(client, get_expense_settlement_payload())
    assert expense_details["verified"] is False


def test_bulk_push_expense_settlement_api(client):
    create_expense_settlement(client, create_expense_settlement_payload())
    bulk_push_expense_settlement(client, bulk_push_expense_settlement_payload())
    expense_details = get_expense_settlement(client, get_expense_settlement_payload())
    assert expense_details["status"] == ERPEntityStatus.PUSHED
