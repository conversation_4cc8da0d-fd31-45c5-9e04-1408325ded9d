import pytest

from finance_erp.domain.auth.models import UserGroupModel, UserModel
from finance_erp.domain.back_office.repository.allowance_repository import (
    AllowanceRepository,
)
from finance_erp.domain.back_office.repository.charge_repository import ChargeRepository
from finance_erp.domain.back_office.repository.cl_control_repository import (
    CLControlRepository,
)
from finance_erp.domain.back_office.repository.folio_details_repository import (
    FolioDetailsRepository,
)
from finance_erp.domain.back_office.repository.gl_control_repository import (
    GLControlRepository,
)
from finance_erp.domain.back_office.repository.ledgers_file import LedgersFileRepository
from finance_erp.domain.back_office.repository.payment_repository import (
    PaymentRepository,
)
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.hotel.models import HotelModel
from finance_erp.domain.ota.models import OtaModel
from finance_erp.domain.payment.models import PGPaymentModel
from finance_erp.domain.payment.repository.payment_summary_repository import (
    PaymentSummaryRepository,
)
from finance_erp.domain.pos.repository.pos_revenue_repository import (
    POSRevenueRepository,
)
from finance_erp.domain.reseller.models import PurchaseInvoiceModel, SaleInvoiceModel
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from finance_erp.domain.settlement.expense.models import ExpenseModel
from finance_erp.domain.settlement.hotel_adjustment.models import HotelAdjustmentModel
from finance_erp.domain.settlement.loan.models import LoanModel
from finance_erp.domain.settlement.tax.models import TaxModel
from finance_erp.domain.settlement.treebo_fee.models import TreeboFeeModel


@pytest.fixture(scope="session")
def user_repo():
    return UserModel()


@pytest.fixture(scope="session")
def user_group_repo():
    return UserGroupModel()


@pytest.fixture(scope="session")
def corporate_repo():
    return CorporateReportRepository()


@pytest.fixture(scope="session")
def hotel_repo():
    return HotelModel()


@pytest.fixture(scope="session")
def sale_invoice_summary_repo():
    return SalesInvoiceSummaryRepository()


@pytest.fixture(scope="session")
def sale_invoice_repo():
    return SalesInvoiceRepository()


@pytest.fixture(scope="session")
def purchase_invoice_repo():
    return PurchaseInvoiceModel()


@pytest.fixture(scope="session")
def ota_repo():
    return OtaModel()


@pytest.fixture(scope="session")
def pg_payment_repo():
    return PGPaymentModel()


@pytest.fixture(scope="session")
def pg_payment_summary_repo():
    return PaymentSummaryRepository()


@pytest.fixture(scope="session")
def expense_repo():
    return ExpenseModel()


@pytest.fixture(scope="session")
def loan_repo():
    return LoanModel()


@pytest.fixture(scope="session")
def tax_repo():
    return TaxModel()


@pytest.fixture(scope="session")
def hotel_adjustment_repo():
    return HotelAdjustmentModel()


@pytest.fixture(scope="session")
def treebo_fee_repo():
    return TreeboFeeModel()


@pytest.fixture(scope="session")
def charge_repo():
    return ChargeRepository()


@pytest.fixture(scope="session")
def allowance_repo():
    return AllowanceRepository()


@pytest.fixture(scope="session")
def payment_repo():
    return PaymentRepository()


@pytest.fixture(scope="session")
def pos_item_repo():
    return POSRevenueRepository()


@pytest.fixture(scope="session")
def folio_repo():
    return FolioDetailsRepository()


@pytest.fixture(scope="session")
def txn_master_repo():
    return TransactionMasterRepository()


@pytest.fixture(scope="session")
def cl_item_repo():
    return CLControlRepository()


@pytest.fixture(scope="session")
def gl_item_repo():
    return GLControlRepository()


@pytest.fixture(scope="session")
def ledger_file_repo():
    return LedgersFileRepository()
