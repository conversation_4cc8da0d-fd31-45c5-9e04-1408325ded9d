class AsyncJobDTO:
    def __init__(self, job_name, data, is_resilient=False, suffix=None):
        self.job_name = job_name
        self.data = data
        self.is_resilient = is_resilient
        self.suffix = suffix


class ScheduledJobDTO(AsyncJobDTO):
    def __init__(self, job_name, data, eta, is_resilient=False):
        super().__init__(job_name, data, is_resilient=is_resilient)
        self.eta = eta
