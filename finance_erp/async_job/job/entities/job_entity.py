from datetime import date, datetime, timedelta
from typing import Optional

from pydantic import BaseModel, Field
from treebo_commons.utils import dateutils

from finance_erp.async_job.job.job_constants import (
    CONCURRENTLY_EXECUTABLE_JOBS,
    JobStatus,
)
from finance_erp.common.exception import DefinitiveFailureException

RETRY_DELAY = 5
MAX_RETRY = 3
MAX_RETRY_FOR_RESILIENT_JOBS = 4


class JobEntity(BaseModel):
    job_name: str
    data: dict
    eta: Optional[datetime] = None
    generated_at: datetime
    job_id: Optional[str] = None
    status: str = Field(default=JobStatus.CREATED)
    picked_at: Optional[datetime] = None
    failure_message: Optional[str] = None
    total_tries: int = 0
    is_resilient: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def should_retry(self, exception=None):
        if isinstance(exception, DefinitiveFailureException):
            return exception.can_retry_once and self.total_tries <= 1
        if self.is_resilient:
            return self.total_tries < MAX_RETRY_FOR_RESILIENT_JOBS
        return self.total_tries < MAX_RETRY

    def _is_next_one_the_last_retry(self):
        if self.is_resilient:
            return self.total_tries >= MAX_RETRY_FOR_RESILIENT_JOBS - 1
        return self.total_tries >= MAX_RETRY - 1

    def derive_next_retry_time(self, exception=None):
        if isinstance(exception, DefinitiveFailureException):
            if exception.retry_after_hours:
                return datetime.now() + timedelta(hours=exception.retry_after_hours)
            return dateutils.datetime_at_max_time_of_day(date.today())
        if self.is_resilient and self._is_next_one_the_last_retry():
            return dateutils.datetime_at_max_time_of_day(date.today())
        return datetime.now() + timedelta(minutes=RETRY_DELAY)

    @property
    def is_safe_for_parallel_execution(self):
        return self.job_name in CONCURRENTLY_EXECUTABLE_JOBS

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
