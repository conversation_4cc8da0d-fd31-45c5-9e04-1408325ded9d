import json
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.async_job.job.aggregates.job_aggregate import JobAggregate
from finance_erp.async_job.job.entities.job_entity import JobEntity
from finance_erp.async_job.job.job_constants import Job<PERSON>tatus
from finance_erp.async_job.job.models import JobModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class JobRepository(BaseRepository):
    def to_entity(self, db_entity: JobModel):
        pass

    @staticmethod
    def to_aggregate(db_entity: JobModel):
        return JobAggregate(
            job_entity=JobEntity(
                job_name=db_entity.job_name,
                data=json.loads(db_entity.data) if db_entity.data else None,
                eta=db_entity.eta,
                generated_at=db_entity.generated_at,
                job_id=db_entity.job_id,
                status=db_entity.status,
                picked_at=db_entity.picked_at,
                failure_message=db_entity.failure_message,
                total_tries=db_entity.total_tries,
                is_resilient=db_entity.is_resilient,
                created_at=db_entity.created_at,
                modified_at=db_entity.modified_at,
            )
        )

    def from_entity(self, domain_entity: JobEntity):
        # noinspection PyArgumentList
        return JobModel(
            job_name=domain_entity.job_name,
            data=json.dumps(domain_entity.data, default=str)
            if domain_entity.data
            else None,
            eta=domain_entity.eta,
            generated_at=domain_entity.generated_at,
            job_id=domain_entity.job_id,
            status=domain_entity.status,
            picked_at=domain_entity.picked_at,
            failure_message=domain_entity.failure_message,
            total_tries=domain_entity.total_tries,
            is_resilient=domain_entity.is_resilient,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def get_oldest_schedulable_job(self):
        query = (
            self.session()
            .query(JobModel)
            .filter(JobModel.status.in_([JobStatus.CREATED, JobStatus.RESCHEDULED]))
            .filter(JobModel.eta <= dateutils.current_datetime())
            .order_by(JobModel.generated_at, JobModel.eta)
        )

        query = query.with_for_update(skip_locked=True)  # Skips rows that are locked
        job = query.first()
        return self.to_aggregate(job) if job else None

    def insert_many(self, job_aggregates: List[JobAggregate]):
        self._bulk_insert_mappings(
            JobModel,
            [
                self.from_entity(job_aggregate.job_entity).mapping_dict()
                for job_aggregate in job_aggregates
            ],
        )
        self.flush_session()

    def get_job(self, pk, with_lock=False):
        if with_lock:
            return self.to_aggregate(
                self.session().query(JobModel).with_for_update(nowait=False).get(pk)
            )

        return self.to_aggregate(self.session().query(JobModel).get(pk))

    def save(self, job_aggregate: JobAggregate):
        self._save(self.from_entity(job_aggregate.job_entity))

    def update_job(self, job_aggregate: JobAggregate):
        entity = job_aggregate.job_entity
        self._bulk_update_mappings(JobModel, [self.from_entity(entity).mapping_dict()])
        self.flush_session()
