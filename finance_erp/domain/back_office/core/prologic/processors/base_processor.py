from typing import Dict, Union

from finance_erp.common.exception import DefinitiveFailureException
from finance_erp.domain.back_office.constants import AccountMasterTypes, GLGroupCodes
from finance_erp.domain.back_office.entity.config_master import (
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.ledger_controls import GLLedgerItem
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from object_registry import register_instance


@register_instance(
    dependencies=[
        CorporateReportRepository,
    ]
)
class BaseProcessor:
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
    ):
        self.corporate_repository = corporate_repository

    def _get_ar_details_from_receive_later_payment(
        self,
        payment: Union[PaymentEntity, POSRevenueItem],
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ) -> CorporateAccount:
        if config_master.is_payment_mode_credit(payment.payment_mode):
            return self._get_debtor_account(
                payment, config_master, debtor_account_cache
            )
        account = config_master.get_account_master(payment.payment_mode_identifier) or (
            payment.payment_mode_identifier != payment.payment_mode
            and config_master.get_account_master(payment.payment_mode)
        )
        return account or self._get_fall_back_account(
            config_master, payment, payment.payment_mode_identifier
        )

    @staticmethod
    def _get_fall_back_account(
        config_master: ConfigMaster, payment: PaymentEntity, company_name: str = None
    ):
        account = config_master.get_account_master(AccountMasterTypes.FALL_BACK_ACCOUNT)
        if not account or not account.account_number:
            return None
        account.company_name = company_name or payment.booking_reference_number
        return account

    def _get_ar_details_for_advance_payment(
        self,
        payment: PaymentEntity,
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ) -> CorporateAccount:
        if not payment.debtor_code:
            return self._get_fall_back_account(config_master, payment)
        return self._get_debtor_account(payment, config_master, debtor_account_cache)

    def _get_debtor_account(
        self,
        payment: PaymentEntity,
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ):
        debtor_code = payment.debtor_code
        if not debtor_code:
            return self._get_fall_back_account(config_master, payment)
        if debtor_code not in debtor_account_cache:
            corporate = self.corporate_repository.get(debtor_code)
            if not corporate:
                raise DefinitiveFailureException(
                    f"Ledger Creation Failed: Corporate not found for debtor code {debtor_code}"
                    f"Payment UUID: {payment.uu_id}"
                    f"Debug message: Check if corporate sync is working properly."
                )
            debtor_account_cache[debtor_code] = CorporateAccount(
                debtor_code=corporate.corporate_code,
                account_number=corporate.external_account_number,
                company_name=corporate.customer_legal_name
                or corporate.customer_trading_name,
            )
        account = debtor_account_cache[debtor_code]
        if not account.account_number:
            fallback_account = self._get_fall_back_account(config_master, payment)
            if not fallback_account:
                return None
            account.account_number = fallback_account.account_number
        return account

    @staticmethod
    def _build_guest_controls_for_payment(
        date, payment, txn_master: TransactionMasterEntity, amount=None
    ):
        # payments are inflow in GL1 so -ve amount
        if amount is None:
            amount = payment.amount
        amount = amount if payment.is_refund else -amount
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        return GLLedgerItem(
            posting_date=date,
            hotel_id=payment.hotel_id,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            credit=credit,
            debit=debit,
            particular=txn_master.particulars,
            is_mergeable=txn_master.merge_gl_entries,
            txn_amount=amount,
            txn_group=GLGroupCodes.GUEST_GROUP_CODE,
        )

    @staticmethod
    def _build_debtor_controls(
        date, payment, txn_master: TransactionMasterEntity, amount=None
    ):
        # payments are outflow in GL5 so +ve
        if amount is None:
            amount = payment.amount
        amount = amount if not payment.is_refund else -amount
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        return GLLedgerItem(
            posting_date=date,
            hotel_id=payment.hotel_id,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            credit=credit,
            debit=debit,
            particular=txn_master.particulars,
            is_mergeable=txn_master.merge_gl_entries,
            txn_amount=amount,
            txn_group=GLGroupCodes.DEBTOR_GROUP_CODE,
        )
