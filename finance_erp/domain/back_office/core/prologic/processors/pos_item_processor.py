from typing import Dict, List

from finance_erp.domain.back_office.constants import GLGroupCodes, GLRevenueTypes
from finance_erp.domain.back_office.core.prologic.processors.base_processor import (
    BaseProcessor,
)
from finance_erp.domain.back_office.entity.config_master import (
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    ErrorFileItem,
    GLLedgerItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from object_registry import register_instance


@register_instance(
    dependencies=[
        CorporateReportRepository,
    ]
)
class PosItemProcessor(BaseProcessor):
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
    ):
        super().__init__(corporate_repository)

    def process(
        self,
        date,
        pos_items: List[POSRevenueItem],
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ):
        ledger_controls = LedgerControls()

        for item in pos_items:
            self._build_gl_control_for_payment(
                date, item, config_master, ledger_controls, debtor_account_cache
            )

            self._build_gl_control_for_charges(
                date,
                item,
                config_master,
                ledger_controls,
            )

        return ledger_controls

    def _build_gl_control_for_payment(
        self,
        date,
        item: POSRevenueItem,
        config_master: ConfigMaster,
        ledger_controls: LedgerControls,
        debtor_account_cache: Dict[str, CorporateAccount],
    ):
        txn_type = (
            GLRevenueTypes.PAYMENT if not item.is_allowance else GLRevenueTypes.REFUND
        )
        txn_master = self._get_txn_master(
            item.payment_method,
            item.revenue_center,
            txn_type,
            config_master,
            payment_sub_type=item.payment_mode_sub_type,
        )
        if not txn_master or not txn_master.gl_code:
            self._capture_error_on_payments(
                item,
                ledger_controls,
                txn_type,
                gl_code_missing=txn_master and not txn_master.gl_code,
            )
            return ledger_controls

        amount = item.amount * -1
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        ledger_controls.gl_controls.append(
            GLLedgerItem(
                posting_date=date,
                hotel_id=item.hotel_id,
                gl_code=txn_master.gl_code,
                txn_id=txn_master.transaction_id,
                credit=credit,
                debit=debit,
                particular=txn_master.particulars,
                is_mergeable=txn_master.merge_gl_entries,
                txn_amount=amount,
                txn_group=GLGroupCodes.GUEST_GROUP_CODE,
            )
        )
        if config_master.is_payment_mode_receive_later(
            item.payment_method, item.payment_mode_sub_type
        ):
            self._build_debtor_control_for_receive_later_payment(
                date,
                item,
                txn_master,
                config_master,
                ledger_controls,
                debtor_account_cache,
            )
        return ledger_controls

    def _build_debtor_control_for_receive_later_payment(
        self,
        date,
        item: POSRevenueItem,
        txn_master,
        config_master,
        ledger_controls,
        debtor_account_cache,
    ):
        # Generate GL control for debtor payments GL5
        amount = item.amount
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        ledger_controls.gl_controls.append(
            GLLedgerItem(
                posting_date=date,
                hotel_id=item.hotel_id,
                gl_code=txn_master.gl_code,
                txn_id=txn_master.transaction_id,
                credit=credit,
                debit=debit,
                particular=txn_master.particulars,
                is_mergeable=txn_master.merge_gl_entries,
                txn_amount=amount,
                txn_group=GLGroupCodes.DEBTOR_GROUP_CODE,
            )
        )
        # Generate CL control for debtor payments
        ar_account = self._get_ar_details_from_receive_later_payment(
            item, config_master, debtor_account_cache
        )
        if not ar_account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping CL entry for receive later payment",
                    identifier=item.bill_id,
                    identifier_type="bill_id",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {item.payment_mode} {item.amount} with revenue center: {item.revenue_center}",
                )
            )
            return
        ledger_controls.cl_controls.append(
            CLLedgerItem(
                account_number=ar_account.account_number,
                account_name=ar_account.company_name,
                guest_name=item.guest_name,
                txt_date=item.pos_bill_date,
                txn_amount=amount,
                hotel_id=item.hotel_id,
                posting_date=date,
                booking_id=item.reservation_id,
            )
        )

    def _build_gl_control_for_charges(
        self,
        date,
        item: POSRevenueItem,
        config_master: ConfigMaster,
        ledger_controls: LedgerControls,
    ):
        tax_txn_type = (
            GLRevenueTypes.CHARGE if not item.is_allowance else GLRevenueTypes.ALLOWANCE
        )
        txn_master = self._get_txn_master(
            item.sku_id,
            item.revenue_center,
            tax_txn_type,
            config_master,
        )
        if txn_master and txn_master.gl_code:
            ledger_controls.gl_controls.append(
                self._build_gl_control(
                    date,
                    item.pretax_amount,
                    txn_master,
                    item.is_allowance,
                )
            )
        else:
            self._capture_error_on_charges(
                item,
                ledger_controls,
                tax_txn_type,
                gl_code_missing=txn_master and not txn_master.gl_code,
            )

        for tax in item.tax_details:
            tax_amount = float(tax.get("tax_amount"))
            if not tax_amount:
                continue
            txn_type = (
                GLRevenueTypes.TAX_ON_CHARGE
                if not item.is_allowance
                else GLRevenueTypes.TAX_ON_ALLOWANCE
            )
            tax_txn_master = self._get_txn_master(
                item.sku_id,
                item.revenue_center,
                txn_type,
                config_master,
                tax=tax,
            )
            if tax_txn_master and tax_txn_master.gl_code:
                ledger_controls.gl_controls.append(
                    self._build_gl_control(
                        date,
                        tax_amount,
                        tax_txn_master,
                        item.is_allowance,
                    )
                )
            else:
                self._capture_error_on_tax_item(
                    item,
                    ledger_controls,
                    tax,
                    tax_txn_type,
                    txn_type,
                    gl_code_missing=tax_txn_master and not tax_txn_master.gl_code,
                )

        return ledger_controls

    @staticmethod
    def _get_txn_master(
        identifier,
        revenue_center,
        txn_type,
        config_master,
        payment_sub_type=None,
        tax=None,
    ):
        try:
            return config_master.get_txn_master(
                txn_type=txn_type,
                identifier=identifier,
                revenue_center=revenue_center,
                tax_percentage=tax.get("tax_value") if tax else None,
                tax_type=tax.get("tax_code") if tax else None,
                payment_sub_type=payment_sub_type,
            )
        except KeyError:
            return None

    @staticmethod
    def _build_gl_control(
        date,
        amount,
        txn_master: TransactionMasterEntity,
        is_allowance=False,
    ):
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        return GLLedgerItem(
            posting_date=date,
            hotel_id=txn_master.hotel_id,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            credit=credit,
            debit=debit,
            particular=txn_master.particulars,
            is_mergeable=txn_master.merge_gl_entries,
            txn_amount=amount,
            txn_group=GLGroupCodes.GUEST_GROUP_CODE,
        )

    @staticmethod
    def _capture_error_on_tax_item(
        item: POSRevenueItem,
        ledger_controls,
        tax,
        tax_txn_type,
        txn_type,
        gl_code_missing=False,
    ):
        tax_percentage = tax.get("tax_value")
        tax_type = tax.get("tax_code")
        error_file_reason = f"due to missing {'txn master' if not gl_code_missing else 'GLCode'} (non room order)"
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {tax_txn_type}",
                identifier=item.sku_id,
                identifier_type="sku_id",
                reason=error_file_reason,
                other_details=f"({tax_type}: {tax_percentage}) - {item.uu_id} - {item.pretax_amount} in {item.revenue_center}",
            )
        )

    @staticmethod
    def _capture_error_on_charges(
        item: POSRevenueItem, ledger_controls, tax_txn_type, gl_code_missing=False
    ):
        error_file_reason = f"due to missing {'txn master' if not gl_code_missing else 'GLCode'} (non room order)"
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {tax_txn_type}",
                identifier=item.sku_id,
                identifier_type="sku_id",
                reason=error_file_reason,
                other_details=f"{item.uu_id} - {item.pretax_amount} in {item.revenue_center}",
            )
        )

    @staticmethod
    def _capture_error_on_payments(
        item: POSRevenueItem, ledger_controls, txn_type, gl_code_missing=False
    ):
        error_file_reason = f"due to missing {'txn master' if not gl_code_missing else 'GLCode'} (non room  payment)"
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {txn_type}",
                identifier=item.payment_method,
                identifier_type="payment_mode",
                reason=error_file_reason,
                other_details=f"{item.uu_id} - {item.pretax_amount} in {item.revenue_center}",
            )
        )
