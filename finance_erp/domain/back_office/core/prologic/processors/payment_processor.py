from typing import Dict, List

from finance_erp.domain.back_office.constants import GLGroupCodes
from finance_erp.domain.back_office.core.prologic.processors.base_processor import (
    BaseProcessor,
)
from finance_erp.domain.back_office.entity.config_master import (
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    ErrorFileItem,
    GLLedgerItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        CorporateReportRepository,
    ]
)
class PaymentProcessor(BaseProcessor):
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
    ):
        super().__init__(corporate_repository)

    def process(
        self,
        date,
        payment_data: List[PaymentEntity],
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ):
        ledger_controls = LedgerControls()

        for payment in payment_data:
            if payment.is_refund and config_master.is_refund_in_exclusion_list(
                payment.payment_mode
            ):
                continue
            txn_master: TransactionMasterEntity = self._get_txn_master(
                payment, config_master
            )
            if not txn_master or not txn_master.gl_code:
                self._capture_error_for_payment(
                    ledger_controls,
                    payment,
                    gl_code_missing=txn_master and not txn_master.gl_code,
                )
                continue
            if payment.is_advance:
                self._process_advance_payment(
                    date,
                    payment,
                    txn_master,
                    config_master,
                    ledger_controls,
                    debtor_account_cache,
                )
            else:
                ledger_controls.gl_controls.append(
                    self._build_guest_controls_for_payment(
                        date,
                        payment,
                        txn_master,
                    )
                )

            if config_master.is_payment_mode_receive_later(
                payment.payment_mode, payment.payment_mode_sub_type
            ):
                self._process_receive_later_payment(
                    date,
                    payment,
                    txn_master,
                    config_master,
                    ledger_controls,
                    debtor_account_cache,
                )

        return ledger_controls

    @staticmethod
    def _capture_error_for_payment(ledger_controls, payment, gl_code_missing=False):
        error_file_reason = (
            f"due to missing {'txn master' if not gl_code_missing else 'GLCode'}"
        )
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped txn {payment.payment_type}",
                identifier=payment.payment_mode,
                identifier_type="payment_mode",
                reason=error_file_reason,
                other_details=f"{payment.payment_mode} {payment.bill_id} - {payment.amount} in {payment.revenue_center}",
            )
        )

    @staticmethod
    def _get_txn_master(payment: PaymentEntity, config_master: ConfigMaster):
        try:
            return config_master.get_txn_master(
                txn_type=payment.payment_type,
                identifier=payment.payment_mode,
                revenue_center=payment.revenue_center,
                payment_sub_type=payment.payment_mode_sub_type,
            )
        except KeyError:
            return None

    def _process_advance_payment(
        self,
        date,
        payment: PaymentEntity,
        txn_master,
        config_master,
        ledger_controls,
        debtor_account_cache,
    ):
        # Generate GL controls for deposit and advance
        ledger_controls.gl_controls.append(
            self._build_deposit_controls(
                date,
                payment,
                txn_master,
            )
        )
        account = self._get_ar_details_for_advance_payment(
            payment,
            config_master,
            debtor_account_cache,
        )
        if not account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping CL entry for advance payment",
                    identifier=payment.booking_reference_number,
                    identifier_type="booking_reference_number",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {payment.bill_id} {payment.payment_mode} {payment.amount}",
                )
            )
            return

        # Generate CL control for the advance
        ledger_controls.cl_controls.append(
            self._build_cl_control(
                date,
                config_master,
                account,
                payment,
                for_advance=True,
            )
        )

    def _process_receive_later_payment(
        self,
        date,
        payment,
        txn_master,
        config_master,
        ledger_controls,
        debtor_account_cache,
    ):
        # Generate GL control for debtor payments
        ledger_controls.gl_controls.append(
            self._build_debtor_controls(
                date,
                payment,
                txn_master,
            )
        )
        # Generate CL control for debtor payments
        account = self._get_ar_details_from_receive_later_payment(
            payment, config_master, debtor_account_cache
        )
        if not account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping CL entry for receive later payment",
                    identifier=payment.booking_reference_number,
                    identifier_type="booking_reference_number",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {payment.bill_id} {payment.payment_mode} {payment.amount} Debtor: {payment.debtor_code or 'NA'}",
                )
            )
            return
        ledger_controls.cl_controls.append(
            self._build_cl_control(
                date,
                config_master,
                account,
                payment,
            )
        )

    @staticmethod
    def _build_cl_control(
        date,
        config_master,
        ar_account: CorporateAccount,
        payment: PaymentEntity,
        for_advance=False,
    ):
        if for_advance:
            amount = payment.amount if payment.is_refund else -payment.amount
        else:
            amount = -payment.amount if payment.is_refund else payment.amount
        payment_type = config_master.get_cl_payment_type_for_crs_payment_mode(
            for_advance, payment.payment_type, payment.crs_payment_mode
        )
        return CLLedgerItem(
            account_number=ar_account.account_number,
            account_name=ar_account.company_name,
            guest_name=payment.guest_name,
            txt_date=payment.date_of_payment,
            txn_amount=amount,
            folio_number=int(payment.folio_number),
            booking_id=payment.booking_id,
            room_number=payment.room_number,
            checkin_date=payment.checkin_date,
            checkout_date=payment.checkout_date,
            posting_date=date,
            hotel_id=payment.hotel_id,
            payment_type=payment_type,
        )

    @staticmethod
    def _build_deposit_controls(
        date,
        payment: PaymentEntity,
        txn_master: TransactionMasterEntity,
    ):
        # payments are inflow in GL7 so -ve amount
        amount = payment.amount if payment.is_refund else -payment.amount
        debit, credit = (0, amount) if amount >= 0 else (-amount, 0)
        return GLLedgerItem(
            posting_date=date,
            hotel_id=payment.hotel_id,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            credit=credit,
            debit=debit,
            particular=txn_master.particulars,
            is_mergeable=txn_master.merge_gl_entries,
            txn_amount=amount,
            txn_group=GLGroupCodes.DEPOSIT_GROUP_CODE,
        )
