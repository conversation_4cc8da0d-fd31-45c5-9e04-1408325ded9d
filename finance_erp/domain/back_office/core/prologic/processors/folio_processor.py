from datetime import date
from typing import Dict, List, Optional, Tuple

from finance_erp.common.exception import DefinitiveFailureException
from finance_erp.domain.back_office.constants import CLPaymentTypes
from finance_erp.domain.back_office.core.prologic.processors.base_processor import (
    BaseProcessor,
)
from finance_erp.domain.back_office.entity.config_master import (
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    ErrorFileItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[CorporateReportRepository])
class FolioProcessor(BaseProcessor):
    def __init__(self, corporate_repository: CorporateReportRepository):
        super().__init__(corporate_repository)

    def process(
        self,
        posting_date: date,
        folios: List[FolioDetailsEntity],
        payment_data: List[PaymentEntity],
        config_master: ConfigMaster,
        debtor_account_cache: Dict[str, CorporateAccount],
    ) -> LedgerControls:
        ledger_controls = LedgerControls()
        payment_data_map: Dict[Tuple[str, int], PaymentEntity] = {
            (payment.bill_id, payment.payment_id): payment for payment in payment_data
        }

        for folio in folios:
            self._process_folio(
                posting_date,
                folio,
                payment_data_map,
                config_master,
                ledger_controls,
                debtor_account_cache,
            )

        return ledger_controls

    def _process_folio(
        self,
        posting_date: date,
        folio: FolioDetailsEntity,
        payment_data_map: Dict[Tuple[str, int], PaymentEntity],
        config_master: ConfigMaster,
        ledger_controls: LedgerControls,
        debtor_account_cache: Dict[str, CorporateAccount],
    ) -> None:
        for folio_payment in folio.payment_split_details:
            payment: PaymentEntity = self._find_payment_or_raise(
                folio, folio_payment, payment_data_map
            )
            if payment.is_refund and config_master.is_refund_in_exclusion_list(
                payment.payment_mode
            ):
                continue

            customer_advance_txn_master: Optional[TransactionMasterEntity] = (
                self._get_txn_master_for_advance_payment(
                    config_master,
                    payment,
                    folio_payment,
                    folio,
                    ledger_controls,
                    is_reversal=folio.is_invoice_reissued,
                )
                if payment.is_advance
                else None
            )

            if folio.is_invoice_locked and customer_advance_txn_master:
                self._transfer_advance_to_guest_control(
                    config_master,
                    customer_advance_txn_master,
                    posting_date,
                    debtor_account_cache,
                    folio,
                    folio_payment,
                    ledger_controls,
                    payment,
                )

            if folio.is_invoice_reissued:
                self._handle_invoice_reissued(
                    config_master,
                    customer_advance_txn_master,
                    posting_date,
                    debtor_account_cache,
                    folio,
                    folio_payment,
                    ledger_controls,
                    payment,
                )

    def _handle_invoice_reissued(
        self,
        config_master: ConfigMaster,
        customer_advance_txn_master: Optional[TransactionMasterEntity],
        posting_date: date,
        debtor_account_cache: Dict[str, CorporateAccount],
        folio: FolioDetailsEntity,
        folio_payment,
        ledger_controls: LedgerControls,
        payment: PaymentEntity,
    ) -> None:
        if customer_advance_txn_master:
            # Reverse the advance payment from GL1 + re-enter in CL
            self._reverse_advance_transfer_from_guest_control(
                config_master,
                customer_advance_txn_master,
                posting_date,
                debtor_account_cache,
                folio,
                folio_payment,
                ledger_controls,
                payment,
            )

        # all non advance payments and receive later payments(irrespective of advance or not)
        if not payment.is_advance or config_master.is_payment_mode_receive_later(
            payment.payment_mode, payment.payment_mode_sub_type
        ):
            txn_master: Optional[
                TransactionMasterEntity
            ] = self._get_txn_master_for_payment(
                config_master,
                payment,
                folio_payment,
                folio,
                ledger_controls,
                is_reversal=True,
            )

            if not txn_master:
                return

            # Reverse the payment amount from in GL1 if not advance
            # Both receive later (card) and received immediately payments (cash) are reversed
            if not payment.is_advance:
                ledger_controls.gl_controls.append(
                    self._build_guest_controls_for_payment(
                        posting_date,
                        payment,
                        txn_master,
                        -folio_payment.amount,  # Reverse the payment amount
                    )
                )

            # Reverse "receive later" payment from Gl5 and CL (advance and non-advance)
            if config_master.is_payment_mode_receive_later(
                payment.payment_mode, payment.payment_mode_sub_type
            ):
                self._reverse_receive_later_payment(
                    posting_date,
                    payment,
                    folio,
                    -folio_payment.amount,  # Reverse the payment amount
                    txn_master,
                    config_master,
                    ledger_controls,
                    debtor_account_cache,
                )

    def _reverse_advance_transfer_from_guest_control(
        self,
        config_master: ConfigMaster,
        customer_advance_txn_master: TransactionMasterEntity,
        posting_date: date,
        debtor_account_cache: Dict[str, CorporateAccount],
        folio: FolioDetailsEntity,
        folio_payment,
        ledger_controls: LedgerControls,
        payment: PaymentEntity,
    ) -> None:
        # Reverse transfer corresponding amount from guest control (G1)
        # Use customer advance txn master to reverse the amount
        # For reversal, simply reverse the sign of the amount and
        # add to guest ledger (-ve sign on the amount to reverse)
        ledger_controls.gl_controls.append(
            self._build_guest_controls_for_payment(
                posting_date,
                payment,
                customer_advance_txn_master,
                -folio_payment.amount,
            )
        )
        account = self._get_ar_details_for_advance_payment(
            payment, config_master, debtor_account_cache
        )
        if not account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping CL entry (reverse advance transfer on reissue)",
                    identifier=payment.booking_reference_number,
                    identifier_type="booking_reference_number",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {payment.bill_id} {payment.payment_mode} {payment.amount} Debtor: {payment.debtor_code or 'NA'}",
                )
            )
            return
        # Re-enter the advance in CL file since we removed the advance from G1
        ledger_controls.cl_controls.append(
            self._build_cl_control(
                posting_date,
                account,
                folio,
                payment,
                folio_payment.amount,
                for_advance=True,
            )
        )

    def _transfer_advance_to_guest_control(
        self,
        config_master: ConfigMaster,
        customer_advance_txn_master: TransactionMasterEntity,
        posting_date: date,
        debtor_account_cache: Dict[str, CorporateAccount],
        folio: FolioDetailsEntity,
        folio_payment,
        ledger_controls: LedgerControls,
        payment: PaymentEntity,
    ) -> None:
        # Transfer the payment amount in folio to guest control (G1)
        ledger_controls.gl_controls.append(
            self._build_guest_controls_for_payment(
                posting_date, payment, customer_advance_txn_master, folio_payment.amount
            )
        )
        account = self._get_ar_details_for_advance_payment(
            payment, config_master, debtor_account_cache
        )
        if not account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping CL entry (transfer of advance during invoicing)",
                    identifier=payment.booking_reference_number,
                    identifier_type="booking_reference_number",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {payment.bill_id} {payment.payment_mode} {payment.amount} Debtor: {payment.debtor_code or 'NA'}",
                )
            )
            return
        # Also reverse the advance from CL file since we transferred the amount to G1
        # Use -ve sign on the amount to reverse
        ledger_controls.cl_controls.append(
            self._build_cl_control(
                posting_date,
                account,
                folio,
                payment,
                -folio_payment.amount,
                for_advance=True,
            )
        )

    def _reverse_receive_later_payment(
        self,
        posting_date: date,
        payment: PaymentEntity,
        folio: FolioDetailsEntity,
        amount: float,
        txn_master: TransactionMasterEntity,
        config_master: ConfigMaster,
        ledger_controls: LedgerControls,
        debtor_account_cache: Dict[str, CorporateAccount],
    ) -> None:
        # Reverse amount for debtor control
        ledger_controls.gl_controls.append(
            self._build_debtor_controls(posting_date, payment, txn_master, amount)
        )
        # Reverse CL control for debtor payments
        account = self._get_ar_details_from_receive_later_payment(
            payment, config_master, debtor_account_cache
        )
        if not account:
            ledger_controls.skipped_transactions.append(
                ErrorFileItem(
                    issue_type="Skipping reversal of CL entry (reissue) for receive later payment",
                    identifier=payment.booking_reference_number,
                    identifier_type="booking_reference_number",
                    reason="Fallback account/account_number is missing",
                    other_details=f"Unable to find AR account for payment {payment.bill_id} {payment.payment_mode} {payment.amount} Folio: {folio.folio_number} Debtor: {payment.debtor_code or 'NA'}",
                )
            )
            return
        ledger_controls.cl_controls.append(
            self._build_cl_control(
                posting_date,
                account,
                folio,
                payment,
                amount,
            )
        )

    @staticmethod
    def _find_payment_or_raise(
        folio: FolioDetailsEntity,
        folio_payment,
        payment_data_map: Dict[Tuple[str, int], PaymentEntity],
    ) -> PaymentEntity:
        payment: Optional[PaymentEntity] = payment_data_map.get(
            (folio.bill_id, folio_payment.payment_id)
        )
        if not payment:
            raise DefinitiveFailureException(
                f"Unable to find payment for folio {folio.folio_number}, payment ID {folio_payment.payment_id}, "
                f"and bill ID {folio.bill_id} for folio payment {folio_payment}"
                f"Debug Message: Check for the reason why the not moved to fin ERP in first place."
            )
        return payment

    def _get_txn_master_for_payment(
        self,
        config_master: ConfigMaster,
        payment: PaymentEntity,
        folio_payment,
        folio: FolioDetailsEntity,
        ledger_controls: LedgerControls,
        is_reversal: bool,
    ) -> Optional[TransactionMasterEntity]:
        try:
            txn_master = config_master.get_txn_master(
                txn_type=payment.payment_type,
                identifier=payment.payment_mode,
                revenue_center=payment.revenue_center,
                payment_sub_type=payment.payment_mode_sub_type,
            )
            if txn_master.gl_code:
                return txn_master
            self._log_missing_txn_master(
                payment,
                folio,
                folio_payment,
                ledger_controls,
                is_reversal,
                gl_code_missing=True,
            )
        except KeyError:
            self._log_missing_txn_master(
                payment, folio, folio_payment, ledger_controls, is_reversal
            )
            return None

    def _get_txn_master_for_advance_payment(
        self,
        config_master: ConfigMaster,
        payment: PaymentEntity,
        folio_payment,
        folio: FolioDetailsEntity,
        ledger_controls: LedgerControls,
        is_reversal: bool,
    ) -> Optional[TransactionMasterEntity]:
        try:
            txn_master = config_master.get_customer_advance_master()
            if txn_master.gl_code:
                return txn_master
            self._log_missing_advance_txn_master(
                payment,
                folio,
                folio_payment,
                ledger_controls,
                is_reversal,
                gl_code_missing=True,
            )
        except KeyError:
            self._log_missing_advance_txn_master(
                payment,
                folio,
                folio_payment,
                ledger_controls,
                is_reversal,
            )
            return None

    @staticmethod
    def _log_missing_txn_master(
        payment: PaymentEntity,
        folio: FolioDetailsEntity,
        folio_payment,
        ledger_controls: LedgerControls,
        is_reversal: bool,
        gl_code_missing=False,
    ) -> None:
        error_file_reason = (
            f"due to missing {'txn master' if not gl_code_missing else 'GLCode'}"
        )
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped {'reversal' if is_reversal else ''} {payment.payment_type}",
                identifier=payment.payment_mode,
                identifier_type="payment_mode",
                reason=error_file_reason,
                other_details=f"{payment.bill_id} - Folio {folio.folio_number} for folio payment {folio_payment.amount} in {payment.revenue_center}",
            )
        )

    @staticmethod
    def _log_missing_advance_txn_master(
        payment: PaymentEntity,
        folio: FolioDetailsEntity,
        folio_payment,
        ledger_controls: LedgerControls,
        is_reversal: bool,
        gl_code_missing=False,
    ) -> None:
        error_file_reason = (
            f"due to missing {'txn master' if not gl_code_missing else 'GLCode'}"
        )
        ledger_controls.skipped_transactions.append(
            ErrorFileItem(
                issue_type=f"Skipped advance {'reversal' if is_reversal else ''} {payment.payment_type}",
                identifier=payment.payment_mode,
                identifier_type="payment_mode",
                reason=error_file_reason,
                other_details=f"{payment.bill_id} - Folio {folio.folio_number} for folio payment {folio_payment.amount} in {payment.revenue_center}",
            )
        )

    @staticmethod
    def _build_cl_control(
        posting_date: date,
        ar_account: CorporateAccount,
        folio: FolioDetailsEntity,
        payment: PaymentEntity,
        amount: float,
        for_advance=False,
    ) -> CLLedgerItem:
        return CLLedgerItem(
            account_number=ar_account.account_number,
            account_name=ar_account.company_name,
            guest_name=folio.guest_name,
            txt_date=payment.date_of_payment,
            txn_amount=amount if payment.is_refund else -amount,
            folio_number=int(payment.folio_number),
            booking_id=payment.booking_id,
            room_number=payment.room_number,
            checkin_date=payment.checkin_date,
            checkout_date=payment.checkout_date,
            posting_date=posting_date,
            hotel_id=folio.hotel_id,
            payment_type=CLPaymentTypes.ROOM_PLUS_TAX if for_advance else None,
        )
