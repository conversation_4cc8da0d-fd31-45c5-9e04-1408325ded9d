from collections import defaultdict
from typing import Any, Dict, List

from finance_erp.domain.back_office.constants import (
    FolioStatus,
    GLGroupAggregateCodes,
    GLRevenueTypes,
)
from finance_erp.domain.back_office.core.prologic.processors.charge_processor import (
    ChargeProcessor,
)
from finance_erp.domain.back_office.core.prologic.processors.folio_processor import (
    FolioProcessor,
)
from finance_erp.domain.back_office.core.prologic.processors.payment_processor import (
    PaymentProcessor,
)
from finance_erp.domain.back_office.core.prologic.processors.pos_item_processor import (
    PosItemProcessor,
)
from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.entity.charge_entity import ChargeEntity
from finance_erp.domain.back_office.entity.config_master import (
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    <PERSON>rrorFileItem,
    GLLedgerItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.payment_entity import PaymentEntity
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.repository.allowance_repository import (
    AllowanceRepository,
)
from finance_erp.domain.back_office.repository.charge_repository import ChargeRepository
from finance_erp.domain.back_office.repository.folio_details_repository import (
    FolioDetailsRepository,
)
from finance_erp.domain.back_office.repository.payment_repository import (
    PaymentRepository,
)
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from finance_erp.domain.pos.repository.pos_revenue_repository import (
    POSRevenueRepository,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        PaymentProcessor,
        ChargeProcessor,
        PosItemProcessor,
        FolioProcessor,
        ChargeRepository,
        AllowanceRepository,
        PaymentRepository,
        FolioDetailsRepository,
        POSRevenueRepository,
    ]
)
class DayEndLedgerDataGenerator:
    def __init__(
        self,
        payment_processor: PaymentProcessor,
        charge_processor: ChargeProcessor,
        pos_item_processor: PosItemProcessor,
        folio_processor: FolioProcessor,
        charge_repository: ChargeRepository,
        allowance_repository: AllowanceRepository,
        payment_repository: PaymentRepository,
        folio_repository: FolioDetailsRepository,
        pos_revenue_item_repository: POSRevenueRepository,
    ):
        self.payment_processor = payment_processor
        self.charge_processor = charge_processor
        self.pos_item_processor = pos_item_processor
        self.folio_processor = folio_processor
        self.charge_repository = charge_repository
        self.allowance_repository = allowance_repository
        self.payment_repository = payment_repository
        self.pos_revenue_item_repository = pos_revenue_item_repository
        self.folio_repository = folio_repository

    def generate(self, date, hotel_id, config_master: ConfigMaster):
        charge_data: List[ChargeEntity] = self.charge_repository.fetch(hotel_id, date)
        allowance_data: List[AllowanceEntity] = self.allowance_repository.fetch(
            hotel_id, date
        )
        if allowance_data:
            # Due to CRS tax calculation tax percentage in the allowance can be different from the charge
            # eg of few wrong rounding in CRS (2.5 -> 2.52, 2.57 | 10 -> 11.2)
            self._enrich_charge_tax_in_allowance(allowance_data, hotel_id)

        payment_data: List[PaymentEntity] = self.payment_repository.fetch(
            hotel_id, date
        )
        pos_revenue_items: List[
            POSRevenueItem
        ] = self.pos_revenue_item_repository.fetch(hotel_id, date)
        debtor_account_cache: Dict[str, CorporateAccount] = {}
        ledger_controls: LedgerControls = self.payment_processor.process(
            date,
            payment_data,
            config_master,
            debtor_account_cache,
        )
        charge_ledger_controls: LedgerControls = self.charge_processor.process(
            date,
            charge_data,
            allowance_data,
            config_master,
        )
        pos_ledger_controls: LedgerControls = self.pos_item_processor.process(
            date,
            pos_revenue_items,
            config_master,
            debtor_account_cache,
        )

        folios, payments = self._get_folios_and_related_payments(date, hotel_id)

        folio_ledger_controls: LedgerControls = self.folio_processor.process(
            date,
            folios,
            payments,
            config_master,
            debtor_account_cache,
        )

        # Optimise this if memory leak is observed
        ledger_controls += (
            charge_ledger_controls + pos_ledger_controls + folio_ledger_controls
        )

        ledger_controls.gl_controls = self.merge_gl_ledger_controls(
            ledger_controls.gl_controls
        )
        ledger_controls.cl_controls = self.merge_cl_ledger_controls(
            ledger_controls.cl_controls
        )
        ledger_controls.gl_controls = self.add_aggregated_control(
            ledger_controls,
            config_master,
        )

        return ledger_controls

    def _enrich_charge_tax_in_allowance(
        self, allowance_data: List[AllowanceEntity], hotel_id
    ):
        charges_associated_with_allowances = self.charge_repository.fetch(
            hotel_id=hotel_id,
            charge_identifiers=[
                (allowance.bill_id, allowance.charge_id) for allowance in allowance_data
            ],
        )
        identifier_charge_map = {
            (charge.bill_id, charge.charge_id): charge
            for charge in charges_associated_with_allowances
        }
        for allowance in allowance_data:
            charge: ChargeEntity = identifier_charge_map.get(
                (allowance.bill_id, allowance.charge_id)
            )
            allowance.tax_details_of_charge = charge.tax_details if charge else []

    def _get_folios_and_related_payments(self, date, hotel_id):
        folios_locked: List[FolioDetailsEntity] = self.folio_repository.fetch(
            hotel_id,
            posting_date=date,
            status=FolioStatus.INVOICE_LOCKED,
        )
        folios_reissued: List[FolioDetailsEntity] = self.folio_repository.fetch(
            hotel_id,
            posting_date=date,
            status=FolioStatus.INVOICE_REISSUED,
        )
        folios = folios_locked + folios_reissued
        # Tune this if query is slowing down
        payment_identifier = [
            (folio.bill_id, payment.payment_id)
            for folio in folios
            for payment in folio.payment_split_details
        ]
        if not payment_identifier:
            return folios, []
        payment_identifier = list(set(payment_identifier))
        payments: List[PaymentEntity] = self.payment_repository.fetch(
            hotel_id, bill_id_payment_id=payment_identifier
        )
        return folios, payments

    @staticmethod
    def merge_gl_ledger_controls(gl_controls: List[GLLedgerItem]):
        gl_control_items = []
        gl_code_mapper = defaultdict(lambda: {})

        for control in gl_controls:
            if not control.is_mergeable:
                gl_control_items.append(control)
                continue

            if control.gl_code not in gl_code_mapper[control.txn_group]:
                gl_code_mapper[control.txn_group][control.gl_code] = control
            else:
                existing_control = gl_code_mapper[control.txn_group][control.gl_code]
                existing_control.txn_amount += control.txn_amount
                existing_control.debit += control.debit
                existing_control.credit += control.credit
                gl_code_mapper[control.txn_group][control.gl_code] = existing_control

        for item in gl_code_mapper.values():
            for control in item.values():
                control.round_values()
                gl_control_items.append(control)

        return gl_control_items

    @staticmethod
    def merge_cl_ledger_controls(cl_controls: List[CLLedgerItem]) -> List[CLLedgerItem]:
        gl_group_mapper: Dict[CLLedgerItem.CLLedgerGroupKey : CLLedgerItem] = dict()

        for control in cl_controls:
            if control.group_key not in gl_group_mapper:
                gl_group_mapper[control.group_key] = control
            else:
                gl_group_mapper[control.group_key].txn_amount += control.txn_amount

        for item in gl_group_mapper.values():
            item.round_values()
        return list(gl_group_mapper.values())

    def add_aggregated_control(
        self, ledger_control: LedgerControls, config_master: ConfigMaster
    ):
        gl_controls: List[GLLedgerItem] = ledger_control.gl_controls
        txn_group_mapper: Dict[int, Any] = dict()
        for control in gl_controls:
            if control.txn_group not in txn_group_mapper:
                group_code = GLGroupAggregateCodes[control.txn_group]
                aggregate_master = self._get_aggregate_master_txn_item_or_log_missing(
                    ledger_control,
                    config_master,
                    group_code,
                )
                if not aggregate_master:
                    continue
                txn_group_mapper[control.txn_group] = self._build_aggregate_item(
                    aggregate_master,
                    control,
                )
            else:
                existing_control = txn_group_mapper[control.txn_group]
                existing_control.txn_amount += control.txn_amount
                existing_control.debit += control.debit
                existing_control.credit += control.credit
                txn_group_mapper[control.txn_group] = existing_control
        for control in txn_group_mapper.values():
            control.round_values()
            gl_controls.append(control)
        return gl_controls

    @staticmethod
    def _get_aggregate_master_txn_item_or_log_missing(
        ledger_control: LedgerControls,
        config_master: ConfigMaster,
        group_code,
    ):
        try:
            aggregate_master = config_master.get_txn_master(
                txn_type=GLRevenueTypes.MASTER_AGGREGATES,
                identifier=str(group_code),
            )
            if aggregate_master.gl_code:
                return aggregate_master
        except KeyError:
            ledger_control.missing_txn_master.add(
                ErrorFileItem(
                    issue_type=f"Txn Master missing for aggregated control G{group_code}",
                    identifier=f"Aggregated GL guest control GL{group_code}",
                    identifier_type="display_name",
                    reason=f"Txn Master missing for aggregated control G{group_code}",
                    other_details="",
                )
            )
        return None

    @staticmethod
    def _build_aggregate_item(txn_master: TransactionMasterEntity, item: GLLedgerItem):
        return GLLedgerItem(
            posting_date=item.posting_date,
            hotel_id=item.hotel_id,
            particular=txn_master.particulars,
            txn_amount=item.txn_amount,
            txn_group=GLGroupAggregateCodes[item.txn_group],
            is_mergeable=False,
            gl_code=txn_master.gl_code,
            txn_id=txn_master.transaction_id,
            debit=item.debit,
            credit=item.credit,
        )
