from typing import List

from finance_erp.domain.back_office.adaptors.allowance_adaptor import AllowanceAdaptor
from finance_erp.domain.back_office.entity.allowance_entity import AllowanceEntity
from finance_erp.domain.back_office.models import AllowanceModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class AllowanceRepository(BaseRepository):
    allowance_adaptor = AllowanceAdaptor()

    def to_entity(self, model):
        return self.allowance_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.allowance_adaptor.to_db_entity(entity)

    def insert_many(self, allowance_data_list: List[AllowanceEntity]):
        self._bulk_insert_mappings(
            AllowanceModel,
            [
                self.from_entity(allowance_data).mapping_dict()
                for allowance_data in allowance_data_list
            ],
        )
        self.flush_session()

    def update_many(self, records):
        self._bulk_update_mappings(
            AllowanceModel,
            [self.from_entity(record).mapping_dict() for record in records],
        )
        self.flush_session()

    def fetch(
        self,
        hotel_id=None,
        posting_date=None,
        limit: int = None,
        offset: int = None,
    ) -> List[AllowanceEntity]:
        query = self.query(AllowanceModel).filter(AllowanceModel.deleted.is_(False))

        if hotel_id:
            query = query.filter(AllowanceModel.hotel_id == hotel_id)

        if posting_date:
            query = query.filter(AllowanceModel.fin_erp_posting_date == posting_date)

        if limit is not None:
            query = query.limit(limit)

        if offset is not None:
            query = query.offset(offset)

        return [self.to_entity(db_model) for db_model in query.all()]

    def get(self, uu_id):
        db_model = super().get_one(AllowanceModel, uu_id=uu_id)
        return self.to_entity(db_model)
