class GLGroupCodes:
    GUEST_GROUP_CODE = 1
    GUEST_GROUP_CODE_AGGREGATE = 2
    DEBTOR_GROUP_CODE = 5
    DEBTOR_GROUP_CODE_AGGREGATE = 6
    DEPOSIT_GROUP_CODE = 7
    DEPOSIT_GROUP_CODE_AGGREGATE = 8


GLGroupAggregateCodes = {
    GLGroupCodes.GUEST_GROUP_CODE: GLGroupCodes.GUEST_GROUP_CODE_AGGREGATE,
    GLGroupCodes.DEBTOR_GROUP_CODE: GLGroupCodes.DEBTOR_GROUP_CODE_AGGREGATE,
    GLGroupCodes.DEPOSIT_GROUP_CODE: GLGroupCodes.DEPOSIT_GROUP_CODE_AGGREGATE,
}


class GLTxnTypes:
    DEBIT = "debit"
    CREDIT = "credit"


class GLRevenueTypes:
    CHARGE = "charge"
    TAX_ON_CHARGE = "tax_on_charge"
    ALLOWANCE = "allowance"
    TAX_ON_ALLOWANCE = "tax_on_allowance"
    PAYMENT = "payment"
    REFUND = "refund"
    MASTER_AGGREGATES = "master_aggregates"


class AccountMasterTypes:
    CUSTOMER_ADVANCE = "customer_advance"
    FALL_BACK_ACCOUNT = "default"


class LedgerFileConstants:
    FIELD_SEPARATOR = ";"
    DATE_FORMAT = "%m%d%Y"
    RECORD_SEPARATOR = "\r"
    CITY_LEDGER_FILE_PREFIX = "MFCL"
    GUEST_LEDGER_FILE_PREFIX = "MFRV"


class ErrorFileConstants:
    FIELD_SEPARATOR = ","


RoomLevelSkuDetails = {
    "sku_category": "stay",
    "sku_name": "room",
    "sku_code": "RoomStay",
}
FRONT_DESK = "Front Desk"


class PaymentModes:
    CITY_LEDGER = "city_ledger"
    PAID_AT_OTA = "paid_at_ota"
    BILLS_ON_HOLD = "bills_on_hold"
    CREDIT_CARD = "credit_card"
    TREEBO_BTC = "treebo_btc"


class IntegratedERPs:
    PROLOGIC = "prologic"
    BUSINESS_CENTRAL = "business_central"

    @classmethod
    def all(cls):
        return [cls.PROLOGIC, cls.BUSINESS_CENTRAL]


class FolioStatus:
    INVOICE_LOCKED = "invoice_locked"
    INVOICE_REISSUED = "invoice_reissued"


class TransactionCategory:
    REVENUE = "REVENUE"
    PAYMENT = "PAYMENT"


class CLPaymentTypes:
    BTT = "BTT"
    BTC = "BTC"
    BOH = "BOH"
    ROOM_PLUS_TAX = "ROOM + TAX"
