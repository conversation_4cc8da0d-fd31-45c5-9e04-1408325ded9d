from collections import namedtuple
from datetime import date, datetime
from typing import ClassVar, List, Optional, Set

from pydantic import BaseModel

from finance_erp.domain.back_office.constants import (
    ErrorFileConstants,
    LedgerFileConstants,
)
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)

LedgerFileFileSeparator = LedgerFileConstants.FIELD_SEPARATOR
ErrorFileFileSeparator = ErrorFileConstants.FIELD_SEPARATOR
DATE_FORMAT = LedgerFileConstants.DATE_FORMAT


class GLLedgerItem(BaseModel):
    posting_date: date
    hotel_id: str
    particular: str
    txn_amount: float
    txn_group: int
    gl_code: Optional[str] = None
    txn_id: Optional[int] = None
    debit: float = 0
    credit: float = 0
    is_mergeable: bool = False
    erp_name: Optional[str] = None
    id: Optional[int] = None
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @property
    def net_value(self) -> float:
        return self.txn_amount * -1

    def to_json(self) -> dict:
        return self.model_dump()

    @property
    def data_repr(self) -> str:
        formatted_date = self.posting_date.strftime(DATE_FORMAT)
        item_details = [
            self.gl_code,
            self.debit,
            self.credit,
            f"{self.txn_id}-{self.particular}",
            self.net_value,
            self.txn_group,
            formatted_date,
        ]
        return LedgerFileFileSeparator.join(map(str, item_details))

    def mark_as_deleted(self):
        self.deleted = True

    def set_erp_name(self, erp_name: str):
        self.erp_name = erp_name

    def round_values(self):
        self.credit = round(self.credit, 2)
        self.debit = round(self.debit, 2)
        self.txn_amount = round(self.txn_amount, 2)

    class Config:
        from_attributes = True
        validate_by_name = True
        frozen = False


class CLLedgerItem(BaseModel):
    CLLedgerGroupKey: ClassVar[namedtuple] = namedtuple(
        "CLLedgerGroupKey",
        ["account_number", "booking_id", "is_refund", "posting_date"],
    )

    account_number: str
    account_name: str
    guest_name: str
    posting_date: date
    hotel_id: str
    txt_date: date
    txn_amount: float
    booking_id: Optional[str] = None
    folio_number: Optional[int] = None
    room_number: Optional[str] = None
    checkout_date: Optional[date] = None
    checkin_date: Optional[date] = None
    erp_name: Optional[str] = None
    payment_type: Optional[str] = None
    id: Optional[int] = None
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @property
    def net_value(self) -> float:
        return self.txn_amount * -1

    def to_json(self) -> dict:
        return self.model_dump()

    @property
    def data_repr(self) -> str:
        posting_date = self.posting_date.strftime(DATE_FORMAT)
        checkin_date = (
            self.checkin_date.strftime(DATE_FORMAT) if self.checkin_date else ""
        )
        checkout_date = (
            self.checkout_date.strftime(DATE_FORMAT) if self.checkout_date else ""
        )
        item_details = [
            self.account_number,
            self.account_name,
            self.guest_name,
            posting_date,
            self.net_value,
            self.folio_number,
            self.room_number,
            checkin_date,
            checkout_date,
            self.payment_type,
        ]
        return LedgerFileFileSeparator.join(map(str, item_details))

    @property
    def group_key(self):
        return self.CLLedgerGroupKey(
            account_number=self.account_number,
            booking_id=self.booking_id,
            is_refund=self.is_refund,
            posting_date=self.posting_date,
        )

    @property
    def is_refund(self) -> bool:
        return self.txn_amount < 0

    def mark_as_deleted(self):
        self.deleted = True

    def set_erp_name(self, erp_name: str):
        self.erp_name = erp_name

    def round_values(self):
        self.txn_amount = round(self.txn_amount, 2)

    class Config:
        from_attributes = True
        validate_by_name = True
        frozen = False


class ErrorFileItem(BaseModel):
    issue_type: str
    identifier: str
    identifier_type: str
    reason: str
    other_details: str

    @property
    def data_repr(self) -> str:
        item_details = [
            self.issue_type,
            self.identifier,
            self.identifier_type,
            self.reason,
            self.other_details,
        ]
        return ErrorFileFileSeparator.join(map(str, item_details))

    def to_json(self) -> dict:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True
        frozen = False


class LedgerControls(BaseModel):
    gl_controls: List[GLLedgerItem] = []
    cl_controls: List[CLLedgerItem] = []
    missing_txn_master: Set[ErrorFileItem] = set()
    skipped_transactions: List[ErrorFileItem] = []

    def has_errors(self) -> bool:
        return bool(self.missing_txn_master or self.skipped_transactions)

    def __add__(self, other: "LedgerControls") -> "LedgerControls":
        if isinstance(other, LedgerControls):
            return LedgerControls(
                gl_controls=self.gl_controls + other.gl_controls,
                cl_controls=self.cl_controls + other.cl_controls,
                missing_txn_master=self.missing_txn_master.union(
                    other.missing_txn_master
                ),
                skipped_transactions=self.skipped_transactions
                + other.skipped_transactions,
            )
        return NotImplemented

    def enrich_erp_name(self, erp_name: str) -> None:
        for ledger in self.gl_controls:
            ledger.set_erp_name(erp_name)
        for ledger in self.cl_controls:
            ledger.set_erp_name(erp_name)

    def populate_txn_entries_with_missing_gl_codes(
        self, txn_master_data: List[TransactionMasterEntity]
    ) -> None:
        for txn in txn_master_data:
            if not txn.gl_code:
                self.missing_txn_master.add(
                    ErrorFileItem(
                        issue_type=f"GL Code Missing in txn master",
                        identifier=txn.display_name,
                        identifier_type="display_name",
                        reason=f"GL Code Missing in txn master",
                        other_details=f"{txn.transaction_id}-{txn.transaction_type}-{txn.identifier}",
                    )
                )

    def to_json(self) -> dict:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True
        str_strip_whitespace = True
