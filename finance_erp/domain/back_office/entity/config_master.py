from collections import defaultdict
from typing import Dict, List, Optional

from pydantic import BaseModel

from core.common.constants import PaymentTypes
from finance_erp.domain.back_office.constants import (
    AccountMasterTypes,
    CLPaymentTypes,
    GLRevenueTypes,
    PaymentModes,
)
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)


class CorporateAccount(BaseModel):
    debtor_code: str
    account_number: Optional[str] = None
    company_name: str


class AccountMaster(BaseModel):
    account: CorporateAccount
    identifier: str


class TransactionMasterLookupTable(BaseModel):
    hotel_id: str
    txn_master_codes: List[TransactionMasterEntity]

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True

    def __init__(self, **data):
        super().__init__(**data)
        self._transaction_lookup_table = defaultdict(lambda: defaultdict(list))
        for transaction_code in self.txn_master_codes:
            self._transaction_lookup_table[transaction_code.transaction_type][
                transaction_code.identifier
            ].append(transaction_code)

    def to_json(self) -> dict:
        return self.model_dump()

    def get_txn_master(
        self,
        txn_type: str,
        identifier: str,
        revenue_center: str = None,
        tax_percentage: float = None,
        tax_type: str = None,
        payment_sub_type: str = None,
    ):
        txn_masters: List[TransactionMasterEntity] = self._transaction_lookup_table[
            txn_type
        ][identifier]
        if txn_type == GLRevenueTypes.MASTER_AGGREGATES:
            if txn_masters:
                return txn_masters[0]
            raise KeyError(f"Transaction Master not found {txn_type} {identifier}")
        for txn_master in txn_masters:
            if txn_master.revenue_center != revenue_center:
                continue
            if txn_type in [
                GLRevenueTypes.TAX_ON_CHARGE,
                GLRevenueTypes.TAX_ON_ALLOWANCE,
            ]:
                if (
                    txn_master.transaction_metadata.tax_value is None
                    or txn_master.transaction_metadata.tax_code is None
                    or tax_percentage is None
                    or tax_type is None
                ):
                    continue
                if (
                    float(txn_master.transaction_metadata.tax_value)
                    == float(tax_percentage)
                    and txn_master.transaction_metadata.tax_code.lower()
                    == tax_type.lower()
                ):
                    return txn_master
            elif payment_sub_type and txn_type in [
                GLRevenueTypes.PAYMENT,
                GLRevenueTypes.REFUND,
            ]:
                if (
                    txn_master.transaction_metadata
                    and txn_master.transaction_metadata.payment_mode_sub_type
                    == payment_sub_type
                ):
                    return txn_master
            else:
                return txn_master
        raise KeyError(
            f"Transaction Master not found {txn_type} {identifier} {revenue_center}"
        )

    def get_customer_advance_master(self):
        try:
            return self._transaction_lookup_table[GLRevenueTypes.PAYMENT][
                AccountMasterTypes.CUSTOMER_ADVANCE
            ][0]
        except (KeyError, IndexError):
            raise KeyError("Transaction Master not found for customer advance")


class ConfigMaster(BaseModel):
    hotel_id: str
    account_master: List[AccountMaster]
    txn_master_codes: List[TransactionMasterEntity]
    receive_later_payment_modes: List[str]
    credit_payment_modes: List[str]
    payment_config: Dict
    refund_modes_to_exclude: Optional[List[str]] = None

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, **data):
        super().__init__(**data)
        self._txn_master_lookup_table = TransactionMasterLookupTable(
            hotel_id=self.hotel_id, txn_master_codes=self.txn_master_codes
        )
        self._account_master_lookup = {
            account.identifier: account for account in self.account_master
        }

    def to_json(self) -> dict:
        return self.model_dump()

    def get_account_master(
        self, identifier: str, raise_if_na=False
    ) -> Optional[CorporateAccount]:
        try:
            return self._account_master_lookup[identifier].account
        except KeyError:
            if not raise_if_na:
                return None
            raise KeyError(f"Account Master not found {identifier}")

    def get_txn_master(
        self,
        txn_type: str,
        identifier: str,
        revenue_center: str = None,
        tax_percentage: float = None,
        tax_type: str = None,
        payment_sub_type: str = None,
    ):
        return self._txn_master_lookup_table.get_txn_master(
            txn_type=txn_type,
            identifier=identifier,
            revenue_center=revenue_center,
            tax_percentage=tax_percentage,
            tax_type=tax_type,
            payment_sub_type=payment_sub_type,
        )

    def get_customer_advance_master(self):
        return self._txn_master_lookup_table.get_customer_advance_master()

    def is_payment_mode_receive_later(
        self, payment_mode: str, payment_sub_type: str = None
    ) -> bool:
        if payment_sub_type:
            payment_mode = f"{payment_mode}#{payment_sub_type}"
        return payment_mode in self.receive_later_payment_modes

    def is_payment_mode_credit(
        self, payment_mode: str, payment_sub_type: str = None
    ) -> bool:
        if payment_sub_type:
            payment_mode = f"{payment_mode}#{payment_sub_type}"
        return payment_mode in self.credit_payment_modes

    def is_refund_in_exclusion_list(self, refund_mode: str) -> bool:
        if not self.refund_modes_to_exclude:
            return False
        return refund_mode in self.refund_modes_to_exclude

    def get_cl_payment_type_for_crs_payment_mode(
        self, for_advance, payment_type: str, crs_payment_mode: str
    ):
        ptt_payments_payment_methods, ptt_refunds_payment_methods = [], []
        if self.payment_config:
            ptt_payments_payment_methods = [
                payment["payment_method"]
                for payment in self.payment_config["payment"]
                if payment["paid_to"] == "treebo"
            ]
            ptt_refunds_payment_methods = [
                refund["payment_method"]
                for refund in self.payment_config["refund"]
                if refund["paid_to"] == "treebo"
            ]
        if for_advance:
            return CLPaymentTypes.ROOM_PLUS_TAX
        elif crs_payment_mode == PaymentModes.CITY_LEDGER:
            return CLPaymentTypes.BTC
        elif crs_payment_mode == PaymentModes.TREEBO_BTC:
            return CLPaymentTypes.BTT
        elif crs_payment_mode == PaymentModes.BILLS_ON_HOLD:
            return CLPaymentTypes.BOH
        elif (
            payment_type == PaymentTypes.PAYMENT.value
            and crs_payment_mode in ptt_payments_payment_methods
        ) or (
            payment_type == PaymentTypes.REFUND.value
            and crs_payment_mode in ptt_refunds_payment_methods
        ):
            return CLPaymentTypes.BTT
        return ""
