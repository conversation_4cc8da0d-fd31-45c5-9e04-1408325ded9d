from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel

from finance_erp.common.constants import LedgersFileStatus

UPDATABLE_FIELDS = ("verified",)


class LedgersFileEntity(BaseModel):
    hotel_id: str
    erp_name: Optional[str] = None
    business_date: date
    ledger_file_type: Optional[str] = None
    ledger_file_path: Optional[str] = None
    ledger_file_name: Optional[str] = None
    remarks: Optional[str] = None
    status: str = LedgersFileStatus.CREATED
    deleted: bool = False
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def to_json(self) -> dict:
        return self.model_dump()

    def update_ledger_file_details(self, ledgers_data) -> None:
        self.ledger_file_path = ledgers_data.ledger_file_path
        self.ledger_file_name = ledgers_data.ledger_file_name

    def update_status(self, ledgers_data) -> None:
        self.status = ledgers_data.status

    def update_remarks(self, ledgers_data) -> None:
        self.remarks = ledgers_data.remarks

    def mark_as_deleted(self) -> None:
        self.deleted = True

    class Config:
        from_attributes = True
        validate_by_name = True
