from finance_erp.domain.back_office.entity.ledgers_file import LedgersFileEntity
from finance_erp.domain.back_office.models import LedgersFileRecord
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class LedgersFileAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: LedgersFileEntity, **kwargs):
        # noinspection PyArgumentList
        return LedgersFileRecord(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            erp_name=domain_entity.erp_name,
            business_date=domain_entity.business_date,
            ledger_file_type=domain_entity.ledger_file_type,
            ledger_file_path=domain_entity.ledger_file_path,
            ledger_file_name=domain_entity.ledger_file_name,
            remarks=domain_entity.remarks,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: LedgersFileRecord, **kwargs):
        return LedgersFileEntity(
            id=db_entity.id,
            hotel_id=db_entity.hotel_id,
            erp_name=db_entity.erp_name,
            business_date=db_entity.business_date,
            ledger_file_type=db_entity.ledger_file_type,
            ledger_file_path=db_entity.ledger_file_path,
            ledger_file_name=db_entity.ledger_file_name,
            remarks=db_entity.remarks,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
