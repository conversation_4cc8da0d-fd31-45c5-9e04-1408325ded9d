from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.folio_detail_entity import FolioDetailsEntity
from finance_erp.domain.back_office.value_objects import PaymentSplitDetails


class FolioDetailsFactory:
    @classmethod
    def create_data_from_dict(cls, data_dict: dict, posting_date) -> FolioDetailsEntity:
        """Create a FolioDetailsEntity from a dictionary of folio details data."""
        payment_split_details = (
            [
                PaymentSplitDetails(**payment_split)
                for payment_split in data_dict.get("payment_split_details", [])
            ]
            if data_dict.get("payment_split_details")
            else None
        )
        data_dict.update(
            {
                "payment_split_details": payment_split_details,
                "uu_id": fin_erp_random_id_generator("FOL", max_length=20),
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
            }
        )
        return FolioDetailsEntity.model_validate(data_dict)
