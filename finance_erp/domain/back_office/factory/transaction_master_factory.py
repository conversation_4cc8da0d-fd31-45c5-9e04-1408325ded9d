from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.value_objects import TransactionMetaData


class TransactionMasterDataFactory:
    @classmethod
    def create_from_dict(cls, data_dict):
        """
        Create a TransactionMasterEntity from a dictionary.
        """
        if data_dict.get("transaction_metadata"):
            data_dict.update(
                {
                    "transaction_metadata": TransactionMetaData.model_validate(
                        data_dict["transaction_metadata"]
                    )
                }
            )
        return TransactionMasterEntity.model_validate(data_dict)
