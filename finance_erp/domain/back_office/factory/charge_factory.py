from datetime import datetime

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.charge_entity import ChargeEntity
from finance_erp.domain.back_office.value_objects import TaxDetails


class ChargeFactory:
    @classmethod
    def create_data_from_dict(cls, data_dict: dict, posting_date) -> ChargeEntity:
        """Create a ChargeEntity from a dictionary of charge data."""
        tax_details = (
            [
                TaxDetails(**tax_detail)
                for tax_detail in data_dict.get("tax_details", [])
            ]
            if data_dict.get("tax_details")
            else None
        )
        data_dict.update(
            {
                "tax_details_of_charge": tax_details,
                "uu_id": fin_erp_random_id_generator("CHG", max_length=20),
                "fin_erp_posting_date": data_dict.get("fin_erp_posting_date")
                or posting_date,
                "created_at": datetime.now(),
                "modified_at": datetime.now(),
            }
        )
        return ChargeEntity.model_validate(data_dict)
