from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel
from ths_common.value_objects import InvoiceBillToInfo, InvoiceIssuedByInfo
from thsc.crs.entities.billing import CreditNote as THSCCreditNote

from finance_erp.domain.crs.value_objects.booking import BookingMeta


class CreditNote(BaseModel):
    credit_note_id: str
    credit_note_number: str
    booking_reference_number: str
    booking_id: str
    customer_external_id: str
    booker_legal_entity_id: str
    issued_to: InvoiceBillToInfo
    issued_by: InvoiceIssuedByInfo
    issued_to_type: str
    invoice_meta: dict
    issued_by_type: str
    pre_tax_amount: float
    tax_amount: float
    post_tax_amount: float
    credit_note_date: date
    is_b2b: bool
    credit_note_type: str
    is_spot_credit: bool
    invoice_ids: List[str]
    credit_note_url: str
    hotel_id: str
    vendor_details: dict
    booking_meta: BookingMeta
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    dirty: bool = False

    def update_from_thsc_entity(self, credit_note_data: THSCCreditNote):
        if (
            credit_note_data.signed_url is not None
            and credit_note_data.signed_url != self.credit_note_url
        ):
            self.credit_note_url = credit_note_data.signed_url
            self.dirty = True

    @property
    def url(self):
        return self.credit_note_url

    @property
    def doc_date(self):
        return self.credit_note_date

    @property
    def number(self):
        return self.credit_note_number

    @property
    def uuid(self):
        return self.credit_note_id

    @property
    def category(self):
        return self.credit_note_type

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
