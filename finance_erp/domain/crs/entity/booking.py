from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, field_validator
from ths_common.constants.booking_constants import AttachmentGroup, BookingStatus
from thsc.crs.entities.billing import Bill as THS<PERSON><PERSON>ill
from thsc.crs.entities.booking import Attachment as THSCAttachment
from thsc.crs.entities.booking import Booking as THS<PERSON>Booking

from finance_erp.common.utils.utils import are_lists_equal
from finance_erp.domain.crs.value_objects.booking import Attachment


class Booking(BaseModel):
    booking_id: str
    reference_number: str
    checkin_date: datetime
    checkout_date: datetime
    actual_checkin_date: Optional[datetime] = None
    actual_checkout_date: Optional[datetime] = None
    channel_code: str
    status: BookingStatus
    subchannel_code: str
    application_code: str
    booker_legal_entity_id: Optional[str] = None
    total_booking_amount: float
    attachments: Optional[List[Attachment]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    dirty: bool = False

    def update_from_thsc_data(
        self,
        thsc_booking: THS<PERSON>Booking,
        thsc_bill: THSCBill,
        attachments: list[THSCAttachment],
    ):
        self.update_checkin_date(thsc_booking.checkin_date)
        self.update_checkout_date(thsc_booking.checkout_date)
        self.update_actual_checkin_date(thsc_booking.actual_checkin_date)
        self.update_actual_checkout_date(thsc_booking.actual_checkout_date)
        self.update_channel_code(thsc_booking.source.channel_code)
        self.update_subchannel_code(thsc_booking.source.subchannel_code)
        self.update_application_code(thsc_booking.source.application_code)
        self.update_booker_legal_entity_id(thsc_booking)
        self.update_total_booking_amount(thsc_bill)
        self.update_attachments(
            [
                Attachment.from_thsc_attachment(attachment)
                for attachment in attachments or []
            ]
        )
        self.update_booking_status(thsc_booking.status)

    def update_checkin_date(self, checkin_date: datetime):
        if self.checkin_date != checkin_date:
            self.checkin_date = checkin_date
            self.dirty = True

    def update_checkout_date(self, checkout_date: datetime):
        if self.checkout_date != checkout_date:
            self.checkout_date = checkout_date
            self.dirty = True

    def update_actual_checkin_date(self, actual_checkin_date: datetime):
        if self.actual_checkin_date != actual_checkin_date:
            self.actual_checkin_date = actual_checkin_date
            self.dirty = True

    def update_actual_checkout_date(self, actual_checkout_date: datetime):
        if self.actual_checkout_date != actual_checkout_date:
            self.actual_checkout_date = actual_checkout_date
            self.dirty = True

    def update_channel_code(self, channel_code: str):
        if self.channel_code != channel_code:
            self.channel_code = channel_code
            self.dirty = True

    def update_subchannel_code(self, subchannel_code: str):
        if self.subchannel_code != subchannel_code:
            self.subchannel_code = subchannel_code
            self.dirty = True

    def update_application_code(self, application_code: str):
        if self.application_code != application_code:
            self.application_code = application_code
            self.dirty = True

    def update_booker_legal_entity_id(self, thsc_booking: THSCBooking):
        booker_legal_entity_id = None
        if (
            thsc_booking.travel_agent_details
            and thsc_booking.travel_agent_details.legal_details
        ):
            booker_legal_entity_id = (
                thsc_booking.travel_agent_details.legal_details.external_reference_id
            )
        elif (
            thsc_booking.company_details and thsc_booking.company_details.legal_details
        ):
            booker_legal_entity_id = (
                thsc_booking.company_details.legal_details.external_reference_id
            )
        if self.booker_legal_entity_id != booker_legal_entity_id:
            self.booker_legal_entity_id = booker_legal_entity_id
            self.dirty = True

    def update_total_booking_amount(self, thsc_bill: THSCBill):
        if self.total_booking_amount != thsc_bill.total_posttax_amount.amount:
            self.total_booking_amount = thsc_bill.total_posttax_amount.amount
            self.dirty = True

    def update_attachments(self, attachments: list[THSCAttachment]):
        current_attachments = sorted(
            self.attachments or [], key=lambda x: x.attachment_id
        )
        new_attachments = sorted(attachments or [], key=lambda x: x.attachment_id)

        if not are_lists_equal(current_attachments, new_attachments):
            self.attachments = attachments
            self.dirty = True

    def update_booking_status(self, status: BookingStatus):
        if self.status != status:
            self.status = status
            self.dirty = True

    def get_booking_request_attachment(self) -> Optional[Attachment]:
        if not self.attachments:
            return None
        for attachment in self.attachments:
            if attachment.attachment_group_id == AttachmentGroup.BOOKING_REQUEST:
                return attachment
        return None

    def is_booking_completely_checked_out(self):
        return (
            self.status == BookingStatus.CHECKED_OUT
            and self.actual_checkout_date is not None
        )

    def is_cancelled_or_no_show(self):
        return self.status in {BookingStatus.CANCELLED, BookingStatus.NOSHOW}

    @field_validator("checkin_date", mode="before")
    def parse_checkin_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("checkout_date", mode="before")
    def parse_checkout_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("actual_checkin_date", mode="before")
    def parse_actual_checkin_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("actual_checkout_date", mode="before")
    def parse_actual_checkout_date(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("created_at", mode="before")
    def parse_created_at(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator("modified_at", mode="before")
    def parse_modified_at(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
