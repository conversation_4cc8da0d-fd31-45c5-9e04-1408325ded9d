from datetime import datetime
from typing import Optional

from pydantic import BaseModel
from ths_common.constants.booking_constants import AttachmentGroup
from thsc.crs.entities.booking import Attachment as THSCAttachment


class BookingMeta(BaseModel):
    booking_id: str
    reference_number: Optional[str] = None
    checkin_date: Optional[datetime]
    checkout_date: Optional[datetime]
    actual_checkin_date: Optional[datetime]
    actual_checkout_date: Optional[datetime]
    channel_code: str
    subchannel_code: str
    application_code: Optional[str] = None
    booker_legal_entity_id: Optional[str] = None
    total_booking_amount: float
    booking_request_attachment: Optional["Attachment"] = None

    @staticmethod
    def from_booking_entity(booking_entity):
        data_dict = {
            "booking_id": booking_entity.booking_id,
            "reference_number": booking_entity.reference_number,
            "checkin_date": booking_entity.checkin_date,
            "checkout_date": booking_entity.checkout_date,
            "actual_checkin_date": booking_entity.actual_checkin_date,
            "actual_checkout_date": booking_entity.actual_checkout_date,
            "channel_code": booking_entity.channel_code,
            "subchannel_code": booking_entity.subchannel_code,
            "application_code": booking_entity.application_code,
            "booker_legal_entity_id": booking_entity.booker_legal_entity_id,
            "total_booking_amount": booking_entity.total_booking_amount,
            "booking_request_attachment": booking_entity.get_booking_request_attachment(),
        }
        return BookingMeta.model_validate(data_dict)

    def to_dict(self):
        return self.model_dump()

    @staticmethod
    def from_dict(data: dict):
        data.update(
            {
                "booking_request_attachment": Attachment.from_dict(
                    data["booking_request_attachment"]
                )
                if data.get("booking_request_attachment")
                else None,
            }
        )
        return BookingMeta.model_validate(data)

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True


class Attachment(BaseModel):
    attachment_id: str
    original_url: str
    display_name: str
    attachment_group_id: Optional[AttachmentGroup] = None
    signed_url: Optional[str] = None
    file_type: str

    def to_dict(self):
        data = self.model_dump()
        data["attachment_group_id"] = (
            data["attachment_group_id"].value if data["attachment_group_id"] else None
        )
        return data

    @staticmethod
    def from_dict(data):
        data_dict = {
            "attachment_id": data.get("attachment_id"),
            "original_url": data.get("original_url"),
            "display_name": data.get("display_name"),
            "attachment_group_id": AttachmentGroup(data.get("attachment_group_id"))
            if data.get("attachment_group_id")
            else None,
            "signed_url": data.get("signed_url"),
            "file_type": data.get("file_type"),
        }
        return Attachment.model_validate(data_dict)

    @staticmethod
    def from_thsc_attachment(thsc_attachment):
        data_dict = {
            "attachment_id": thsc_attachment.attachment_id,
            "original_url": thsc_attachment.original_url,
            "display_name": thsc_attachment.display_name,
            "attachment_group_id": thsc_attachment.attachment_group,
            "signed_url": thsc_attachment.signed_url,
            "file_type": thsc_attachment.file_type,
        }
        return Attachment.model_validate(data_dict)

    def __eq__(self, other):
        if not isinstance(other, (Attachment, THSCAttachment)):
            return False
        return (
            self.attachment_id == other.attachment_id
            and self.attachment_group_id == other.attachment_group_id
            and self.display_name == other.display_name
            and self.original_url == other.original_url
            and self.display_name == other.display_name
            and self.signed_url == other.signed_url
            and self.file_type == other.file_type
        )

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
