from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.value_objects import InvoiceBillToInfo, InvoiceIssuedByInfo

from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.models import InvoiceModel
from finance_erp.domain.crs.value_objects.booking import BookingMeta


class InvoiceAdaptor:
    @staticmethod
    def to_entity(model: InvoiceModel) -> Invoice:
        return Invoice(
            invoice_id=model.invoice_id,
            invoice_number=model.invoice_number,
            status=InvoiceStatus(model.status),
            customer_external_id=model.customer_external_id,
            booker_legal_entity_id=model.booker_legal_entity_id,
            issued_to=InvoiceBillToInfo.from_json(model.issued_to)
            if model.issued_to
            else None,
            issued_by=InvoiceIssuedByInfo.from_json(model.issued_by)
            if model.issued_by
            else None,
            booking_meta=BookingMeta.from_dict(model.booking_meta)
            if model.booking_meta
            else None,
            issued_to_type=model.issued_to_type,
            issued_by_type=model.issued_by_type,
            booking_reference_number=model.booking_reference_number,
            booking_id=model.booking_id,
            is_b2b=model.is_b2b,
            pre_tax_amount=model.pre_tax_amount,
            tax_amount=model.tax_amount,
            post_tax_amount=model.post_tax_amount,
            due_date=model.due_date,
            invoice_date=model.invoice_date,
            invoice_type=model.invoice_type,
            is_spot_credit=model.is_spot_credit,
            invoice_url=model.invoice_url,
            hotel_id=model.hotel_id,
            vendor_details=model.vendor_details,
            frozen_at=model.frozen_at,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def from_entity(entity: Invoice) -> InvoiceModel:
        # noinspection PyArgumentList
        return InvoiceModel(
            invoice_id=entity.invoice_id,
            invoice_number=entity.invoice_number,
            status=entity.status.value,
            customer_external_id=entity.customer_external_id,
            booker_legal_entity_id=entity.booker_legal_entity_id,
            issued_to=entity.issued_to.to_json() if entity.issued_to else None,
            issued_by=entity.issued_by.to_json() if entity.issued_by else None,
            booking_meta=entity.booking_meta.to_dict() if entity.booking_meta else None,
            issued_to_type=entity.issued_to_type,
            issued_by_type=entity.issued_by_type,
            booking_reference_number=entity.booking_reference_number,
            booking_id=entity.booking_id,
            is_b2b=entity.is_b2b,
            pre_tax_amount=entity.pre_tax_amount,
            tax_amount=entity.tax_amount,
            post_tax_amount=entity.post_tax_amount,
            due_date=entity.due_date,
            invoice_date=entity.invoice_date,
            invoice_type=entity.invoice_type,
            is_spot_credit=entity.is_spot_credit,
            invoice_url=entity.invoice_url,
            hotel_id=entity.hotel_id,
            vendor_details=entity.vendor_details,
            frozen_at=entity.frozen_at,
            created_at=entity.created_at,
            modified_at=entity.modified_at,
        )
