from typing import Any, Dict, List

from finance_erp.domain.crs.adaptors.booking_adaptor import BookingAdaptor
from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.models import BookingModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class BookingRepository(BaseRepository):
    def to_entity(self, model: BookingModel) -> Booking:
        return BookingAdaptor.to_entity(model)

    def from_entity(self, entity: Booking) -> BookingModel:
        return BookingAdaptor.from_entity(entity)

    def save(self, entity: Booking) -> Booking:
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def get_by_ids(self, booking_ids: List[str]) -> List[Booking]:
        query = self.query(BookingModel).filter(
            BookingModel.booking_id.in_(booking_ids)
        )
        return [self.to_entity(model) for model in query.all()]

    def get_by_id(self, booking_id: str) -> Booking:
        model = super().get_one(BookingModel, booking_id=booking_id)
        return self.to_entity(model) if model else None

    def update(self, entity: Booking) -> Booking:
        if not entity.dirty:
            return entity
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)
