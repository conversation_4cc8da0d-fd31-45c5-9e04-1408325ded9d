from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel


class ProcessLevelAudit(BaseModel):
    audit_type: Optional[str]
    entity_type: Optional[str]
    event_type: Optional[str]
    event_id: Optional[str]
    event_data: Optional[dict[str, Any]]
    description: Optional[str]
    detailed_trace: Optional[str]
    created_at: Optional[datetime] = None

    def to_dict(self) -> dict:
        return self.model_dump()

    @staticmethod
    def from_json(data: dict) -> "ProcessLevelAudit":
        return ProcessLevelAudit(
            audit_type=data.get("report_name"),
            entity_type=data.get("entity_type"),
            event_id=data.get("event_id"),
            event_type=data.get("event_type"),
            event_data=data.get("event_data"),
            description=data.get("description"),
            detailed_trace=data.get("detailed_trace"),
            created_at=data.get("created_at"),
        )

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
