from finance_erp.common.constants import ProcessNames
from finance_erp.domain.shared_kernel.audit.process_level.audit_repository import (
    ProcessLevelAuditRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.entity import (
    ProcessLevelAudit,
)
from object_registry import register_instance


class IngestionAuditEvents:
    INCOMING_REQUEST_EVENT = "incoming_request_event"
    RETRY_EVENT = "retry_event"
    QUEUING_EVENT = "queuing_event"
    PROCESS_BEGIN_EVENT = "process_begin_event"
    FILTER_EVENT = "filter_event"
    INSERTION_EVENT = "insertion_event"
    UPDATE_EVENT = "update_event"
    FAILURE_EVENT = "failure_event"
    SUCCESS_EVENT = "success_event"


@register_instance(dependencies=[ProcessLevelAuditRepository])
class IngestionAuditService:
    def __init__(self, audit_repository: ProcessLevelAuditRepository):
        self.audit_repository = audit_repository

    @staticmethod
    def create_audit_object(
        event_type,
        event_id,
        report_type,
        failure_message=None,
        error_trace=None,
        stats: dict = None,
    ):
        return ProcessLevelAudit(
            audit_type=ProcessNames.INGESTION,
            entity_type=report_type,
            event_id=event_id,
            event_type=event_type,
            event_data=stats,
            description=failure_message,
            detailed_trace=error_trace,
        )

    def record_incoming_ingestion_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.INCOMING_REQUEST_EVENT,
            event_id,
            report_type,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_retry_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.RETRY_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_queued_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.QUEUING_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_started_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.PROCESS_BEGIN_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_data_filter_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.FILTER_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_data_insertion_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.INSERTION_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_data_update_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.UPDATE_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_failure_event(
        self,
        event_id,
        report_type,
        failure_message,
        error_trace=None,
        stats: dict = None,
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.FAILURE_EVENT,
            event_id,
            report_type,
            failure_message,
            error_trace,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_ingestion_success_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.SUCCESS_EVENT, event_id, report_type, stats=stats
        )
        self.audit_repository.create(audit_event)
