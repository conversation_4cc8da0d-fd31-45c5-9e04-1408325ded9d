from finance_erp.common.constants import ProcessNames
from finance_erp.domain.shared_kernel.audit.process_level.audit_repository import (
    ProcessLevelAuditRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.entity import (
    ProcessLevelAudit,
)
from object_registry import register_instance


class IngestionAuditEvents:
    INCOMING_DATA_PULL_REQUEST_EVENT = "incoming_data_pull_request_event"
    DATA_PULL_PROCESS_BEGIN_EVENT = "data_pull_process_begin_event"
    DATA_PULL_FAILURE_EVENT = "data_pull_failure_event"
    DATA_PULL_SUCCESS_EVENT = "data_pull_success_event"


@register_instance(dependencies=[ProcessLevelAuditRepository])
class DataPullAuditService:
    def __init__(self, audit_repository: ProcessLevelAuditRepository):
        self.audit_repository = audit_repository

    @staticmethod
    def create_audit_object(
        event_type,
        event_id,
        report_type,
        failure_message=None,
        error_trace=None,
        stats: dict = None,
    ):
        return ProcessLevelAudit(
            audit_type=ProcessNames.PULL,
            entity_type=report_type,
            event_id=event_id,
            event_type=event_type,
            event_data=stats,
            description=failure_message,
            detailed_trace=error_trace,
        )

    def record_incoming_data_pull_event(
        self, event_id, report_type, stats: dict = None
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.INCOMING_DATA_PULL_REQUEST_EVENT,
            event_id,
            report_type,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_data_pull_started_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.DATA_PULL_PROCESS_BEGIN_EVENT,
            event_id,
            report_type,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_data_pull_failure_event(
        self,
        event_id,
        report_type,
        failure_message,
        error_trace=None,
        stats: dict = None,
    ):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.DATA_PULL_FAILURE_EVENT,
            event_id,
            report_type,
            failure_message,
            error_trace,
            stats=stats,
        )
        self.audit_repository.create(audit_event)

    def record_data_pull_success_event(self, event_id, report_type, stats: dict = None):
        audit_event = self.create_audit_object(
            IngestionAuditEvents.DATA_PULL_SUCCESS_EVENT,
            event_id,
            report_type,
            stats=stats,
        )
        self.audit_repository.create(audit_event)
