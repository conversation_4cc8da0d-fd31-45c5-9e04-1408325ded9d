from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base

from finance_erp.domain.shared_kernel.orm_base import TimeStampMixin

Base = declarative_base()


class CommunicationLogModel(Base, TimeStampMixin):
    __tablename__ = "communication_log"

    id = Column(Integer, primary_key=True, autoincrement=True)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String, nullable=False)
    communication_type = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False)
    communication_details = Column(JSON)
    notification_id = Column(String(255), nullable=True)
    message = Column(Text, nullable=True)
