from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.entity.stay_summary import StaySummary
from finance_erp.domain.shared_kernel.audit.communication_audit.audit_repository import (
    CommunicationAuditRepository,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.constants import (
    CommunicationStatus,
    CommunicationTypes,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.entity import (
    CommunicationLog,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.value_objects import (
    CorporateCommunicationDetails,
)
from object_registry import register_instance


@register_instance(dependencies=[CommunicationAuditRepository])
class CommunicationAuditService:
    def __init__(self, audit_repository: CommunicationAuditRepository):
        self.audit_repository = audit_repository

    def audit_corporate_stay_summary_dispatch(
        self,
        stay_summary: StaySummary,
        corporate: CorporateEntity = None,
        external_communication_id: str = None,
        status: str = CommunicationStatus.SUCCESS,
        message: str = None,
    ):
        communication_details = (
            CorporateCommunicationDetails(
                corporate_id=corporate.corporate_code,
                corporate_name=corporate.customer_legal_name,
            )
            if corporate
            else None
        )
        audit_entry = self.audit_repository.fetch(
            resource_name=StaySummary.__name__,
            resource_id=stay_summary.stay_summary_id,
            communication_type=CommunicationTypes.CORPORATE_STAY_SUMMARY_DISPATCH,
        )
        if audit_entry:
            audit_entry.update(
                status=status,
                notification_id=external_communication_id,
                message=message,
                communication_details=communication_details,
            )
            return self.audit_repository.update(audit_entry)
        audit_entry = CommunicationLog(
            communication_type=CommunicationTypes.CORPORATE_STAY_SUMMARY_DISPATCH,
            resource_type=stay_summary.__class__.__name__,
            resource_id=stay_summary.stay_summary_id,
            status=status,
            communication_details=communication_details,
            notification_id=external_communication_id,
            message=message,
        )
        return self.audit_repository.create(audit_entry)

    def audit_corporate_invoice_dispatch(
        self,
        invoice: Invoice,
        corporate: CorporateEntity = None,
        external_communication_id: str = None,
        status: str = CommunicationStatus.SUCCESS,
        message: str = None,
    ):
        communication_details = (
            CorporateCommunicationDetails(
                corporate_id=corporate.corporate_code,
                corporate_name=corporate.customer_legal_name,
            )
            if corporate
            else None
        )
        audit_entry = self.audit_repository.fetch(
            resource_name=Invoice.__name__,
            resource_id=invoice.invoice_id,
            communication_type=CommunicationTypes.CORPORATE_INVOICE_DISPATCH,
        )
        if audit_entry:
            audit_entry.update(
                status=status,
                notification_id=external_communication_id,
                message=message,
                communication_details=communication_details,
            )
            return self.audit_repository.update(audit_entry)
        audit_entry = CommunicationLog(
            communication_type=CommunicationTypes.CORPORATE_INVOICE_DISPATCH,
            resource_type=invoice.__class__.__name__,
            resource_id=invoice.invoice_id,
            status=status,
            communication_details=communication_details,
            notification_id=external_communication_id,
            message=message,
        )
        return self.audit_repository.create(audit_entry)

    def audit_corporate_credit_note_dispatch(
        self,
        credit_note: CreditNote,
        corporate: CorporateEntity = None,
        external_communication_id: str = None,
        status: str = CommunicationStatus.SUCCESS,
        message: str = None,
    ):
        communication_details = (
            CorporateCommunicationDetails(
                corporate_id=corporate.corporate_code,
                corporate_name=corporate.customer_legal_name,
            )
            if corporate
            else None
        )
        audit_entry = self.audit_repository.fetch(
            resource_name=CreditNote.__name__,
            resource_id=credit_note.credit_note_id,
            communication_type=CommunicationTypes.CORPORATE_CREDIT_NOTE_DISPATCH,
        )
        if audit_entry:
            audit_entry.update(
                status=status,
                notification_id=external_communication_id,
                communication_details=communication_details,
                message=message,
            )
            return self.audit_repository.update(audit_entry)
        audit_entry = CommunicationLog(
            communication_type=CommunicationTypes.CORPORATE_CREDIT_NOTE_DISPATCH,
            resource_type=credit_note.__class__.__name__,
            resource_id=credit_note.credit_note_id,
            status=status,
            communication_details=communication_details,
            notification_id=external_communication_id,
            message=message,
        )
        return self.audit_repository.create(audit_entry)

    def was_invoice_dispatched(self, invoice: Invoice) -> bool:
        return (
            self.audit_repository.fetch(
                resource_name=Invoice.__name__,
                resource_id=invoice.invoice_id,
                communication_type=CommunicationTypes.CORPORATE_INVOICE_DISPATCH,
                status=CommunicationStatus.SUCCESS,
            )
            is not None
        )

    def was_cn_dispatched(self, credit_note: CreditNote) -> bool:
        return (
            self.audit_repository.fetch(
                resource_name=CreditNote.__name__,
                resource_id=credit_note.credit_note_id,
                communication_type=CommunicationTypes.CORPORATE_CREDIT_NOTE_DISPATCH,
                status=CommunicationStatus.SUCCESS,
            )
            is not None
        )
