from abc import ABC, abstractmethod
from typing import Any, Dict, Type

from pydantic import BaseModel

from finance_erp.domain.shared_kernel.audit.communication_audit.constants import (
    CommunicationTypes,
)


class AbstractCommunicationDetails(BaseModel, ABC):
    @staticmethod
    @abstractmethod
    def to_dict(obj: "AbstractCommunicationDetails") -> Dict[str, Any]:
        raise NotImplementedError("to_dict method not implemented")

    @staticmethod
    @abstractmethod
    def from_dict(data: Dict[str, Any]) -> "AbstractCommunicationDetails":
        raise NotImplementedError("from_dict method not implemented")

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        validate_by_name = True


class CorporateCommunicationDetails(AbstractCommunicationDetails):
    corporate_name: str
    corporate_id: str

    @staticmethod
    def to_dict(obj: "CorporateCommunicationDetails") -> dict:
        return obj.model_dump()

    @staticmethod
    def from_dict(data: dict) -> "CorporateCommunicationDetails":
        return CorporateCommunicationDetails(**data)

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        validate_by_name = True


COMMUNICATION_DETAILS_MAPPER: Dict[str, Type[AbstractCommunicationDetails]] = {
    CommunicationTypes.CORPORATE_STAY_SUMMARY_DISPATCH: CorporateCommunicationDetails,
    CommunicationTypes.CORPORATE_INVOICE_DISPATCH: CorporateCommunicationDetails,
    CommunicationTypes.CORPORATE_CREDIT_NOTE_DISPATCH: CorporateCommunicationDetails,
}
