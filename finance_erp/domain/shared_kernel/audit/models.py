from datetime import datetime
from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import TimeStampMixin


class ProcessLevelAuditModel(Base, TimeStampMixin):
    __tablename__ = "process_level_audits"

    id = Column(Integer, primary_key=True)
    audit_type = Column(String)
    entity_type = Column(String)
    event_type = Column(String)
    event_id = Column(String)
    event_data = Column(JSON)
    description = Column(String)
    detailed_trace = Column(String)


class EntityLevelAuditModel(Base):
    __tablename__ = "entity_level_audits"

    id = Column(Integer, primary_key=True)
    entity_name = Column(String)
    uu_id = Column(String)
    event_type = Column(String)
    event_data = Column(String)
    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)

    def mapping_dict(self) -> Dict:
        return {
            "entity_name": self.entity_name,
            "uu_id": self.uu_id,
            "event_type": self.event_type,
            "event_data": self.event_data,
        }
