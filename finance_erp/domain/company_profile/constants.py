from finance_erp.common.constants import InvoiceTypes


class ProfilePOCRoles:
    TREEBO_POC = "Sales POC"
    INSIDE_SALES_POC = "Inside Sales POC"
    FINANCE_ADMIN = "Finance POC"
    PRIMARY_ADMIN = "Booking POC"
    HOTEL_FINANCE_POC = "Hotel Finance POC"


class InvoiceDispatchOption:
    DISABLED = "disabled"
    ONLY_CREDIT = "only-credit"
    ALL_CREDIT = "all-credit"
    ONLY_SPOT_CREDIT = "only-spot-credit"
    PAID_INVOICE = "paid"
    ALL = "all"

    INVOICE_TYPE_MAPPING = {
        ALL_CREDIT: [InvoiceTypes.SPOT_CREDIT, InvoiceTypes.NON_SPOT_CREDIT],
        PAID_INVOICE: [InvoiceTypes.NON_CREDIT],
        ONLY_SPOT_CREDIT: [InvoiceTypes.SPOT_CREDIT],
        ONLY_CREDIT: [InvoiceTypes.NON_SPOT_CREDIT],
        ALL: [
            InvoiceTypes.SPOT_CREDIT,
            InvoiceTypes.NON_SPOT_CREDIT,
            InvoiceTypes.NON_CREDIT,
        ],
    }

    @staticmethod
    def is_enabled(value):
        return value in (
            InvoiceDispatchOption.ONLY_CREDIT,
            InvoiceDispatchOption.ALL_CREDIT,
            InvoiceDispatchOption.ONLY_SPOT_CREDIT,
            InvoiceDispatchOption.PAID_INVOICE,
            InvoiceDispatchOption.ALL,
        )

    @staticmethod
    def invoice_types(value):
        return InvoiceDispatchOption.INVOICE_TYPE_MAPPING.get(value)
