from finance_erp.application.common.utils import get_country_code, get_state_code
from finance_erp.common.constants import GSTType
from finance_erp.domain.company_profile.dto.company_profiles_dto import SubEntity
from finance_erp.domain.company_profile.dto.corporate_dto import CorporateDto
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity


class CorporateFactory:
    @staticmethod
    def create_from_corporate_dto(dto: CorporateDto):
        data_dict = {
            "customer_legal_name": dto.customer_legal_name,
            "customer_trading_name": dto.customer_trading_name,
            "address": dto.address,
            "city": dto.city,
            "phone_number": dto.phone_number,
            "post_code": dto.post_code,
            "email": dto.email,
            "credit_limit": dto.credit_limit,
            "credit_period": dto.credit_period,
            "country_code": dto.country_code,
            "pan": dto.pan,
            "state_code": dto.state_code,
            "gstin": dto.gstin,
            "gst_customer_type": dto.gst_customer_type,
            "corporate_code": dto.corporate_code,
            "tan_number": dto.tan_number,
            "billing_period": dto.billing_period,
            "external_account_number": dto.external_account_number,
            "communication_settings": dto.communication_settings,
            "corporate_pocs": dto.corporate_pocs,
        }
        return CorporateEntity.model_validate(data_dict)

    @staticmethod
    def create_from_company_profile_entity(entity: SubEntity):
        admin = entity.primary_admin
        phone = (
            entity.phone_number
            if entity.phone_number
            else admin.phone_number
            if admin
            else None
        )
        email = (
            entity.email_id
            if entity.email_id
            else admin.email_ids[0]
            if admin and admin.email_ids
            else None
        )
        address = entity.address
        data_dict = {
            "customer_legal_name": entity.legal_entity_name,
            "customer_trading_name": entity.trade_name,
            "address": entity.address_string,
            "city": address.city if address else None,
            "phone_number": phone.number if phone else None,
            "post_code": address.pincode if address else None,
            "email": email,
            "credit_limit": entity.credit_limit if entity.credit_settings else None,
            "country_code": get_country_code(address.country)
            if address and address.country
            else None,
            "pan": entity.pan_number,
            "state_code": get_state_code(address.state)
            if address and address.state
            else None,
            "gstin": entity.gstin,
            "gst_customer_type": GSTType.REGISTERED.value
            if entity.gstin
            else GSTType.UNREGISTERED.value,
            "corporate_code": entity.superhero_company_code,
            "tan_number": entity.tan_number,
            "billing_period": entity.billing_period,
            "communication_settings": entity.communication_settings,
            "corporate_pocs": entity.point_of_contacts,
            "credit_period": entity.credit_period,
            "external_account_number": entity.external_account_number,
        }
        return CorporateEntity.model_validate(data_dict)
