from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.domain.company_profile.value_object import (
    CommunicationSettings,
    CorporatePOC,
)
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class CorporateAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CorporateEntity, **kwargs):
        # noinspection PyArgumentList
        return CorporateModel(
            customer_legal_name=domain_entity.customer_legal_name,
            corporate_code=domain_entity.corporate_code,
            customer_trading_name=domain_entity.customer_trading_name,
            address=domain_entity.address,
            city=domain_entity.city,
            phone_number=domain_entity.phone_number,
            post_code=domain_entity.post_code,
            email=domain_entity.email,
            credit_limit=domain_entity.credit_limit,
            credit_period=domain_entity.credit_period,
            country_code=domain_entity.country_code,
            pan=domain_entity.pan,
            state_code=domain_entity.state_code,
            gstin=domain_entity.gstin,
            gst_customer_type=domain_entity.gst_customer_type,
            tan_number=domain_entity.tan_number,
            billing_period=domain_entity.billing_period,
            communication_settings=domain_entity.communication_settings.to_dict()
            if domain_entity.communication_settings
            else None,
            corporate_pocs=[poc.to_dict() for poc in domain_entity.corporate_pocs]
            if domain_entity.corporate_pocs
            else None,
            next_billing_date=domain_entity.next_billing_date,
            is_billing_enabled=domain_entity.is_billing_enabled,
            external_account_number=domain_entity.external_account_number,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: CorporateModel, **kwargs):
        return CorporateEntity(
            customer_legal_name=db_entity.customer_legal_name,
            corporate_code=db_entity.corporate_code,
            customer_trading_name=db_entity.customer_trading_name,
            address=db_entity.address,
            city=db_entity.city,
            phone_number=db_entity.phone_number,
            post_code=db_entity.post_code,
            email=db_entity.email,
            credit_limit=db_entity.credit_limit,
            credit_period=db_entity.credit_period,
            country_code=db_entity.country_code,
            pan=db_entity.pan,
            state_code=db_entity.state_code,
            gstin=db_entity.gstin,
            gst_customer_type=db_entity.gst_customer_type,
            tan_number=db_entity.tan_number,
            billing_period=db_entity.billing_period,
            external_account_number=db_entity.external_account_number,
            communication_settings=CommunicationSettings.from_dict(
                db_entity.communication_settings
            )
            if db_entity.communication_settings
            else None,
            corporate_pocs=[
                CorporatePOC.from_dict(poc) for poc in db_entity.corporate_pocs
            ]
            if db_entity.corporate_pocs
            else None,
            _next_billing_date=db_entity.next_billing_date,
            _is_billing_enabled=db_entity.is_billing_enabled,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            is_new=False,
        )
