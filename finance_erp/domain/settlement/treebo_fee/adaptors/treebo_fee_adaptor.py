import datetime

from finance_erp.domain.settlement.treebo_fee.entity.treebo_fee import TreeboFeeEntity
from finance_erp.domain.settlement.treebo_fee.models import TreeboFeeModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class TreeboFeeAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: TreeboFeeEntity, **kwargs):
        # noinspection PyArgumentList
        return TreeboFeeModel(
            hotel_code=domain_entity.hotel_code,
            settlement_date=domain_entity.settlement_date,
            remarks=domain_entity.remarks,
            uu_id=domain_entity.uu_id,
            description=domain_entity.description,
            amount=domain_entity.amount,
            gst_percent=domain_entity.gst_percent,
            entry_type=domain_entity.entry_type,
            hsn_code=domain_entity.hsn_code,
            doc_type=domain_entity.doc_type,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: TreeboFeeModel, **kwargs):
        return TreeboFeeEntity(
            hotel_code=db_entity.hotel_code,
            settlement_date=db_entity.settlement_date,
            remarks=db_entity.remarks,
            description=db_entity.description,
            amount=db_entity.amount,
            gst_percent=db_entity.gst_percent,
            entry_type=db_entity.entry_type,
            hsn_code=db_entity.hsn_code,
            doc_type=db_entity.doc_type,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
        )
