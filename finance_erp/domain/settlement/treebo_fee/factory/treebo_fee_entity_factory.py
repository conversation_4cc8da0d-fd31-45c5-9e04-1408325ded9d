from finance_erp.domain.settlement.treebo_fee.entity.treebo_fee import TreeboFeeEntity


class TreeboFeeFactory:
    @staticmethod
    def create_settlement_treebo_fee_from_data_dict(data_dict: dict):
        data_dict.update(
            {
                "uu_id": f'{data_dict["entry_type"]}/{data_dict["hotel_code"]}/{data_dict.get("settlement_date")}',
            }
        )
        return TreeboFeeEntity.model_validate(data_dict)
