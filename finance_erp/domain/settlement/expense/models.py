from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class ExpenseModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "settlement_expense"

    hotel_code = Column(String)
    posting_date = Column(Date)
    uu_id = Column(String, primary_key=True)
    tds_per = Column(DECIMAL)
    invoice_number = Column(String)
    invoice_amount = Column(DECIMAL)
    invoice_date = Column(Date)
    entry_type = Column(String)
    hsn_code = Column(String)
    doc_type = Column(String)
    remarks = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "hotel_code": self.hotel_code,
            "posting_date": self.posting_date,
            "remarks": self.remarks,
            "tds_per": self.tds_per,
            "uu_id": self.uu_id,
            "invoice_number": self.invoice_number,
            "invoice_amount": self.invoice_amount,
            "invoice_date": self.invoice_date,
            "entry_type": self.entry_type,
            "hsn_code": self.hsn_code,
            "doc_type": self.doc_type,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
