import datetime

from finance_erp.domain.settlement.hotel_adjustment.entity.hotel_adjustment import (
    HotelAdjustmentEntity,
)
from finance_erp.domain.settlement.hotel_adjustment.models import HotelAdjustmentModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class HotelAdjustmentAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelAdjustmentEntity, **kwargs):
        # noinspection PyArgumentList
        return HotelAdjustmentModel(
            hotel_code=domain_entity.hotel_code,
            posting_date=domain_entity.posting_date,
            uu_id=domain_entity.uu_id,
            amount=domain_entity.amount,
            invoice_number=domain_entity.invoice_number,
            invoice_amount=domain_entity.invoice_amount,
            invoice_date=domain_entity.invoice_date
            if domain_entity.invoice_date
            else None,
            entry_type=domain_entity.entry_type,
            adjustment_type=domain_entity.adjustment_type,
            doc_type=domain_entity.doc_type,
            remarks=domain_entity.remarks,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: HotelAdjustmentModel, **kwargs):
        return HotelAdjustmentEntity(
            hotel_code=db_entity.hotel_code,
            posting_date=db_entity.posting_date,
            amount=db_entity.amount,
            invoice_number=db_entity.invoice_number,
            invoice_amount=db_entity.invoice_amount,
            invoice_date=db_entity.invoice_date if db_entity.invoice_date else None,
            entry_type=db_entity.entry_type,
            adjustment_type=db_entity.adjustment_type,
            doc_type=db_entity.doc_type,
            remarks=db_entity.remarks,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
        )
