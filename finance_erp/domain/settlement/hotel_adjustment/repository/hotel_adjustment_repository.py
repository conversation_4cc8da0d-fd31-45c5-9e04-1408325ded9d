from datetime import time
from typing import List

from treebo_commons.utils import dateutils

from finance_erp.common.constants import HotelAdjustmentTypes
from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.settlement.hotel_adjustment.adaptors.hotel_adjustment_adaptor import (
    HotelAdjustmentAdaptor,
)
from finance_erp.domain.settlement.hotel_adjustment.entity.hotel_adjustment import (
    HotelAdjustmentEntity,
)
from finance_erp.domain.settlement.hotel_adjustment.models import HotelAdjustmentModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class HotelAdjustmentRepository(BaseRepository):
    settlement_hotel_adjustment_adaptor = HotelAdjustmentAdaptor()

    def to_entity(self, model):
        return self.settlement_hotel_adjustment_adaptor.to_domain_entity(model)

    def from_entity(self, entity):
        return self.settlement_hotel_adjustment_adaptor.to_db_entity(entity)

    def insert_many(
        self, settlement_hotel_adjustment_list: List[HotelAdjustmentEntity]
    ):
        self._bulk_insert_mappings(
            HotelAdjustmentModel,
            [
                self.from_entity(settlement_hotel_adjustment).mapping_dict()
                for settlement_hotel_adjustment in settlement_hotel_adjustment_list
            ],
        )
        self.flush_session()

    def bulk_update_records(
        self, settlement_hotel_adjustment_list: List[HotelAdjustmentEntity]
    ):
        self._bulk_update_mappings(
            HotelAdjustmentModel,
            [
                self.from_entity(settlement_hotel_adjustment).mapping_dict()
                for settlement_hotel_adjustment in settlement_hotel_adjustment_list
            ],
        )
        self.flush_session()

    def get(self, uu_id):
        db_model = super().get_one(HotelAdjustmentModel, uu_id=uu_id)
        return self.to_entity(db_model)

    def get_by_ids(self, uu_ids, for_update=False):
        db_models = self.filter(
            HotelAdjustmentModel,
            HotelAdjustmentModel.uu_id.in_(tuple(uu_ids)),
            for_update=for_update,
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def get_reports_by_date_range(self, from_date, to_date):
        from_date_gte = dateutils.datetime_at_given_time(from_date, time.min)
        to_date_lte = dateutils.datetime_at_given_time(to_date, time.max)
        db_models = (
            self.query(HotelAdjustmentModel)
            .filter(
                HotelAdjustmentModel.modified_at >= from_date_gte,
                HotelAdjustmentModel.modified_at <= to_date_lte,
                HotelAdjustmentModel.deleted == False,
            )
            .order_by(HotelAdjustmentModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]

    def update_record(self, record: HotelAdjustmentEntity):
        self.bulk_update_entities(HotelAdjustmentModel, [record])
        self.flush_session()

    def get_eligible_records_to_push(self):
        db_models = (
            self.session()
            .query(HotelAdjustmentModel)
            .filter(
                HotelAdjustmentModel.verified == True,
                HotelAdjustmentModel.status.in_(
                    ERPEntityStatus.allowed_status_for_data_push()
                ),
                HotelAdjustmentModel.deleted == False,
                HotelAdjustmentModel.adjustment_type != HotelAdjustmentTypes.GST_DEBIT,
            )
            .with_for_update(nowait=True)
            .all()
        )

        return [self.to_entity(db_model) for db_model in db_models]

    def get_pushed_records_by_uuid(self, uu_ids):
        db_models = (
            self.query(HotelAdjustmentModel)
            .filter(
                HotelAdjustmentModel.uu_id.in_(tuple(uu_ids)),
                HotelAdjustmentModel.status == ERPEntityStatus.PUSHED,
            )
            .order_by(HotelAdjustmentModel.modified_at.desc())
        )
        return [self.to_entity(db_model) for db_model in db_models]
