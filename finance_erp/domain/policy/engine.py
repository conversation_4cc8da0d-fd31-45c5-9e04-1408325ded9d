import logging

from finance_erp.common.exception import PolicyAuthException
from finance_erp.domain.policy.rule.ledgers_data_rule import LedgersDataRule
from finance_erp.infrastructure.external_clients.role_privilege.role_manager import (
    RoleManagerClient,
)
from finance_erp.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)

logger = logging.getLogger(__name__)


class RuleEngine(object):
    action_rule_map = dict(ledgers_data_rule=LedgersDataRule)

    @staticmethod
    def action_allowed(action, facts, fail_on_error=False):
        rule = RuleEngine.action_rule_map.get(action)
        assert rule is not None, f"mapping not defined for {action} in action_rule_map"

        privileges = dict()

        if facts.user_type:
            logger.debug(
                "Getting user_type {0} for action {1}".format(facts.user_type, action)
            )
            try:
                role_privilege_dtos = RoleManagerClient().get_privilege_by_role_name(
                    facts.user_type
                )
            except:
                role_privilege_dtos = []

            privileges = RolePrivilegesDTO.array_to_dict(
                role_privilege_dtos=role_privilege_dtos
            )

        try:
            return rule().allow(facts, privileges)
        except PolicyAuthException as pae:
            if fail_on_error:
                raise
            logger.debug(
                "Policy evaluation failed for action: %s: %s", action, pae.message
            )
            return False
