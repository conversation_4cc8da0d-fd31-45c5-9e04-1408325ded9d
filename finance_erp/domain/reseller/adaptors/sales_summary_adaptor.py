from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity
from finance_erp.domain.reseller.models import SalesSummaryModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class SaleInvoiceSummaryAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity=SalesSummaryEntity, **kwargs):
        # noinspection PyArgumentList
        return SalesSummaryModel(
            hsn_code=domain_entity.hsn_code,
            entry_type=domain_entity.entry_type,
            order_date=domain_entity.order_date,
            posting_date=domain_entity.posting_date,
            uvid_date=domain_entity.uvid_date,
            unit_price=domain_entity.unit_price,
            tax_percentage=domain_entity.tax_percentage,
            unique_ref_id=domain_entity.unique_ref_id,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
            cgst=domain_entity.cgst,
            igst=domain_entity.igst,
            sgst=domain_entity.sgst,
            tax_type=domain_entity.tax_type,
        )

    def to_domain_entity(self, db_entity=SalesSummaryModel, **kwargs):

        return SalesSummaryEntity(
            hsn_code=db_entity.hsn_code,
            entry_type=db_entity.entry_type,
            order_date=db_entity.order_date,
            posting_date=db_entity.posting_date,
            uvid_date=db_entity.uvid_date,
            unit_price=db_entity.unit_price,
            tax_percentage=db_entity.tax_percentage,
            unique_ref_id=db_entity.unique_ref_id,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            status=db_entity.status,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            cgst=db_entity.cgst,
            sgst=db_entity.sgst,
            igst=db_entity.igst,
            tax_type=db_entity.tax_type,
            is_new=False,
            is_dirty=False,
        )
