from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.models import SaleInvoiceModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class SaleInvoiceAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity=SalesInvoiceEntity, **kwargs):
        # noinspection PyArgumentList
        return SaleInvoiceModel(
            remarks=domain_entity.remarks,
            hsn_code=domain_entity.hsn_code,
            customer_number=domain_entity.customer_number,
            original_invoice_number=domain_entity.original_invoice_number,
            structure=domain_entity.structure,
            nature_of_supply=domain_entity.nature_of_supply,
            entry_type=domain_entity.entry_type,
            order_date=domain_entity.order_date,
            posting_date=domain_entity.posting_date,
            uvid_date=domain_entity.uvid_date,
            check_in=domain_entity.check_in,
            check_out=domain_entity.check_out,
            reference_number=domain_entity.reference_number,
            state_code=domain_entity.state_code,
            unit_price=domain_entity.unit_price,
            tax_percentage=domain_entity.tax_percentage,
            hotel_name=domain_entity.hotel_name,
            stay_days=domain_entity.stay_days,
            room_type=domain_entity.room_type,
            occupancy=domain_entity.occupancy,
            guest_name=domain_entity.guest_name,
            invoice_number=domain_entity.invoice_number,
            buy_side_invoice_number=domain_entity.buy_side_invoice_number,
            total_invoice_amount=domain_entity.total_invoice_amount,
            hotel_code=domain_entity.hotel_code,
            unique_ref_id=domain_entity.unique_ref_id,
            source=domain_entity.source,
            sub_source=domain_entity.sub_source,
            gst_customer_type=domain_entity.gst_customer_type,
            invoice_charge_type=domain_entity.invoice_charge_type,
            billed_to_legal_name=domain_entity.billed_to_legal_name,
            billed_to_state_code=domain_entity.billed_to_state_code,
            billed_to_gstin=domain_entity.billed_to_gstin,
            source_created_on=domain_entity.source_created_on,
            booking_owner_legal_entity_id=domain_entity.booking_owner_legal_entity_id,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
            aggregation_id=domain_entity.aggregation_id,
            aggregated_at=domain_entity.aggregated_at,
            sgst=domain_entity.sgst,
            cgst=domain_entity.cgst,
            igst=domain_entity.igst,
            tax_type=domain_entity.tax_type,
        )

    def to_domain_entity(self, db_entity=SaleInvoiceModel, **kwargs):

        return SalesInvoiceEntity(
            remarks=db_entity.remarks,
            hsn_code=db_entity.hsn_code,
            customer_number=db_entity.customer_number,
            original_invoice_number=db_entity.original_invoice_number,
            structure=db_entity.structure,
            nature_of_supply=db_entity.nature_of_supply,
            entry_type=db_entity.entry_type,
            order_date=db_entity.order_date,
            posting_date=db_entity.posting_date,
            check_out=db_entity.check_out,
            check_in=db_entity.check_in,
            uvid_date=db_entity.uvid_date,
            reference_number=db_entity.reference_number,
            state_code=db_entity.state_code,
            unit_price=db_entity.unit_price,
            tax_percentage=db_entity.tax_percentage,
            hotel_name=db_entity.hotel_name,
            stay_days=db_entity.stay_days,
            room_type=db_entity.room_type,
            occupancy=db_entity.occupancy,
            guest_name=db_entity.guest_name,
            invoice_number=db_entity.invoice_number,
            buy_side_invoice_number=db_entity.buy_side_invoice_number,
            total_invoice_amount=db_entity.total_invoice_amount,
            hotel_code=db_entity.hotel_code,
            unique_ref_id=db_entity.unique_ref_id,
            source=db_entity.source,
            sub_source=db_entity.sub_source,
            gst_customer_type=db_entity.gst_customer_type,
            invoice_charge_type=db_entity.invoice_charge_type,
            billed_to_legal_name=db_entity.billed_to_legal_name,
            billed_to_state_code=db_entity.billed_to_state_code,
            billed_to_gstin=db_entity.billed_to_gstin,
            source_created_on=db_entity.source_created_on,
            booking_owner_legal_entity_id=db_entity.booking_owner_legal_entity_id,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            aggregation_id=db_entity.aggregation_id,
            aggregated_at=db_entity.aggregated_at,
            sgst=db_entity.sgst,
            cgst=db_entity.cgst,
            igst=db_entity.igst,
            tax_type=db_entity.tax_type,
        )
