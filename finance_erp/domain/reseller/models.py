from typing import Dict

from sqlalchemy import DECIMA<PERSON>, Boolean, Column, Date, DateTime, Index, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class AbstractBaseEntityForInvoiceSummaryModel(Base, TimeStampMixin, DeleteMixin):
    __abstract__ = True

    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    entry_type = Column(String)
    posting_date = Column(Date)
    order_date = Column(Date)
    uvid_date = Column(Date)


class TaxDetailsAttributes(Base):
    __abstract__ = True

    cgst = Column(DECIMAL)
    sgst = Column(DECIMAL)
    igst = Column(DECIMAL)
    tax_type = Column(String)


class BaseEntityModel(AbstractBaseEntityForInvoiceSummaryModel):
    __abstract__ = True

    unit_price = Column(DECIMAL)
    tax_percentage = Column(DECIMAL)
    reference_number = Column(String)
    state_code = Column(String)
    hotel_name = Column(String)
    check_in = Column(Date)
    check_out = Column(Date)
    stay_days = Column(String)
    room_type = Column(String)
    occupancy = Column(String)
    guest_name = Column(String)
    invoice_number = Column(String)
    total_invoice_amount = Column(DECIMAL)
    hotel_code = Column(String)
    source = Column(String)
    sub_source = Column(String)

    verified = Column(Boolean, default=False)


class PurchaseInvoiceModel(BaseEntityModel):
    __tablename__ = "purchase_invoice"

    status = Column(String)

    unique_ref_id = Column(String, primary_key=True)
    vendor_number = Column(String)
    due_date = Column(Date)
    remark = Column(String)
    hsn_code = Column(String)
    original_invoice_number = Column(String)
    source_created_on = Column(Date)
    customer_invoice_number = Column(String)
    structure = Column(String, nullable=False, default="GST")
    nature_of_supply = Column(String, nullable=False, default="B2B")
    gst_vendor_type = Column(String, nullable=False, default="REGISTERED")
    purchase_type = Column(String)
    report_category = Column(String)

    __table_args__ = (
        Index("ix_purchase_invoice_invoice_number", "invoice_number"),
        Index("ix_purchase_invoice_posting_date", "posting_date"),
        Index("ix_purchase_invoice_status", "status"),
        Index("ix_purchase_invoice_customer_invoice_number", "customer_invoice_number"),
        Index("ix_purchase_reference_number", "reference_number"),
        Index("ix_purchase_invoice_hotel_code", "hotel_code"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "vendor_number": self.vendor_number,
            "due_date": self.due_date,
            "remark": self.remark,
            "hsn_code": self.hsn_code,
            "original_invoice_number": self.original_invoice_number,
            "source_created_on": self.source_created_on,
            "structure": self.structure,
            "nature_of_supply": self.nature_of_supply,
            "gst_vendor_type": self.gst_vendor_type,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
            "entry_type": self.entry_type,
            "order_date": self.order_date,
            "posting_date": self.posting_date,
            "reference_number": self.reference_number,
            "state_code": self.state_code,
            "unit_price": self.unit_price,
            "tax_percentage": self.tax_percentage,
            "hotel_name": self.hotel_name,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "stay_days": self.stay_days,
            "room_type": self.room_type,
            "occupancy": self.occupancy,
            "guest_name": self.guest_name,
            "uvid_date": self.uvid_date,
            "invoice_number": self.invoice_number,
            "total_invoice_amount": self.total_invoice_amount,
            "hotel_code": self.hotel_code,
            "unique_ref_id": self.unique_ref_id,
            "source": self.source,
            "sub_source": self.sub_source,
            "customer_invoice_number": self.customer_invoice_number,
            "purchase_type": self.purchase_type,
            "report_category": self.report_category,
        }


class SalesInvoiceStatus(ERPEntityStatus):
    AGGREGATED = "aggregated"


class SaleInvoiceModel(BaseEntityModel, TaxDetailsAttributes):
    __tablename__ = "sale_invoice"

    status = Column(String, default=SalesInvoiceStatus.INGESTED)
    aggregated_at = Column(DateTime)

    unique_ref_id = Column(String, primary_key=True)
    aggregation_id = Column(String)
    structure = Column(String, nullable=False, default="GST")
    nature_of_supply = Column(String, nullable=False, default="B2B")
    customer_number = Column(String)
    gst_customer_type = Column(String)
    billed_to_state_code = Column(String)
    billed_to_gstin = Column(String)
    remarks = Column(String)
    billed_to_legal_name = Column(String)
    source_created_on = Column(Date)
    hsn_code = Column(String)
    original_invoice_number = Column(String)
    invoice_charge_type = Column(String)
    buy_side_invoice_number = Column(String)
    booking_owner_legal_entity_id = Column(String)

    __table_args__ = (
        Index("ix_sale_invoice_invoice_number", "invoice_number"),
        Index("ix_sale_invoice_posting_date", "posting_date"),
        Index("ix_sale_invoice_status", "status"),
        Index(
            "ix_sale_invoice_aggregation_filter",
            "verified",
            "status",
            "deleted",
            "posting_date",
        ),
        Index("ix_sale_invoice_buy_side_invoice_number", "buy_side_invoice_number"),
        Index("ix_sale_invoice_customer_number", "customer_number"),
        Index("ix_sale_invoice_hotel_code", "hotel_code"),
        Index("ix_sale_invoice_reference_number", "reference_number"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "remarks": self.remarks,
            "hsn_code": self.hsn_code,
            "customer_number": self.customer_number,
            "original_invoice_number": self.original_invoice_number,
            "structure": self.structure,
            "nature_of_supply": self.nature_of_supply,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "aggregated_at": self.aggregated_at,
            "aggregation_id": self.aggregation_id,
            "erp_remarks": self.erp_remarks,
            "entry_type": self.entry_type,
            "order_date": self.order_date,
            "posting_date": self.posting_date,
            "reference_number": self.reference_number,
            "state_code": self.state_code,
            "unit_price": self.unit_price,
            "tax_percentage": self.tax_percentage,
            "hotel_name": self.hotel_name,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "stay_days": self.stay_days,
            "room_type": self.room_type,
            "occupancy": self.occupancy,
            "guest_name": self.guest_name,
            "uvid_date": self.uvid_date,
            "invoice_number": self.invoice_number,
            "total_invoice_amount": self.total_invoice_amount,
            "hotel_code": self.hotel_code,
            "unique_ref_id": self.unique_ref_id,
            "source": self.source,
            "sub_source": self.sub_source,
            "gst_customer_type": self.gst_customer_type,
            "invoice_charge_type": self.invoice_charge_type,
            "billed_to_legal_name": self.billed_to_legal_name,
            "billed_to_state_code": self.billed_to_state_code,
            "billed_to_gstin": self.billed_to_gstin,
            "source_created_on": self.source_created_on,
            "buy_side_invoice_number": self.buy_side_invoice_number,
            "booking_owner_legal_entity_id": self.booking_owner_legal_entity_id,
            "cgst": self.cgst,
            "sgst": self.sgst,
            "igst": self.igst,
            "tax_type": self.tax_type,
        }


class SalesSummaryModel(AbstractBaseEntityForInvoiceSummaryModel, TaxDetailsAttributes):
    __tablename__ = "sales_summary"

    status = Column(String)
    unit_price = Column(DECIMAL)
    tax_percentage = Column(DECIMAL)
    unique_ref_id = Column(String, primary_key=True)
    hsn_code = Column(String)

    __table_args__ = (
        Index("ix_sales_summary_status_deleted", "status", "deleted"),
        Index("ix_sales_summary_posting_date", "posting_date"),
        Index("ix_sales_summary_status", "status"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
            "hsn_code": self.hsn_code,
            "entry_type": self.entry_type,
            "order_date": self.order_date,
            "posting_date": self.posting_date,
            "unit_price": self.unit_price,
            "tax_percentage": self.tax_percentage,
            "uvid_date": self.uvid_date,
            "unique_ref_id": self.unique_ref_id,
            "cgst": self.cgst,
            "sgst": self.sgst,
            "igst": self.igst,
            "tax_type": self.tax_type,
        }
