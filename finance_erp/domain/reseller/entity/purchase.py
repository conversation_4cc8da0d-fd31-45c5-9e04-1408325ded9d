from datetime import date, datetime
from typing import Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.common.constants import (
    GSTType,
    NatureOfSupply,
    PurchaseInvoiceReportCategory,
    PurchaseInvoiceTypes,
    Structure,
)
from finance_erp.common.utils.checksum_creator import get_data_hash_20
from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin, ERPEntityStatus
from finance_erp.domain.shared_kernel.base_entity_for_sales_purchase import BaseEntity


@change_tracker
class PurchaseInvoiceEntity(BaseEntity, ERPEntityMixin, ChangeTrackerMixin):
    vendor_number: Optional[str] = None
    due_date: Optional[date] = None
    remark: Optional[str] = None
    hotel_code: str
    hsn_code: str
    original_invoice_number: Optional[str] = None
    source_created_on: Optional[date] = None
    structure: str = Structure.GST.value
    nature_of_supply: str = NatureOfSupply.B2B.value
    gst_vendor_type: str = GSTType.REGISTERED.value
    customer_invoice_number: Optional[str] = None
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    cost_center_id: Optional[str] = None
    purchase_type: Optional[str] = None
    report_category: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        if not self.purchase_type:
            self.purchase_type = PurchaseInvoiceTypes.RESELLER_INVOICE.value
        if not self.report_category:
            self.report_category = (
                PurchaseInvoiceReportCategory.RESELLER_PURCHASE_INVOICE_REPORT.value
            )

    def to_json(self):
        """Convert the object to a JSON"""
        return self.model_dump()

    def mark_as_unverified(self):
        self.verified = True

    def get_unique_identifier(self):
        return self.unique_ref_id

    def update(self, data: dict):
        self.entry_type = data.get("entry_type")
        self.order_date = data.get("order_date")
        self.posting_date = data.get("posting_date")
        self.reference_number = data.get("reference_number")
        self.state_code = data.get("state_code")
        self.unit_price = data.get("unit_price")
        self.tax_percentage = data.get("tax_percentage")
        self.hotel_name = data.get("hotel_name")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.stay_days = data.get("stay_days")
        self.room_type = data.get("room_type")
        self.occupancy = data.get("occupancy")
        self.guest_name = data.get("guest_name")
        self.uvid_date = data.get("uvid_date")
        self.invoice_number = data.get("invoice_number")
        self.total_invoice_amount = data.get("total_invoice_amount")
        self.hotel_code = data.get("hotel_code")
        self.source = data.get("source")
        self.sub_source = get_data_hash_20(data.get("sub_source"))
        self.customer_invoice_number = data.get("customer_invoice_number")
        self.vendor_number = data.get("vendor_number")
        self.due_date = data.get("due_date")
        self.erp_remarks = data.get("erp_remarks")
        self.hsn_code = data.get("hsn_code")
        self.original_invoice_number = data.get("original_invoice_number")
        self.source_created_on = data.get("source_created_on")
        self.structure = data.get("structure", Structure.GST.value)
        self.nature_of_supply = data.get("nature_of_supply", NatureOfSupply.B2B.value)
        self.gst_vendor_type = data.get("gst_vendor_type", GSTType.REGISTERED.value)
        self.purchase_type = data.get("purchase_type", self.purchase_type)
        self.report_category = data.get("report_category", self.report_category)
        self.verified = True
        self.status = ERPEntityStatus.INGESTED

    class Config:
        from_attributes = True
        validate_by_name = True
