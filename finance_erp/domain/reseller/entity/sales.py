# -*- coding: utf-8 -*-
from collections import namedtuple
from datetime import date, datetime
from typing import ClassVar, Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.common.constants import GSTType, NatureOfSupply, Structure
from finance_erp.common.utils.checksum_creator import get_data_hash_20
from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin
from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.domain.shared_kernel.base_entity_for_sales_purchase import BaseEntity


@change_tracker
class SalesInvoiceEntity(BaseEntity, ERPEntityMixin, ChangeTrackerMixin):
    SalesInvoiceGroupKey: ClassVar[namedtuple] = namedtuple(
        "SalesInvoiceGroupKey",
        ["entry_type", "posting_date", "tax_percentage", "hsn_code", "tax_type"],
    )
    customer_number: Optional[str] = None
    billed_to_state_code: str
    billed_to_gstin: Optional[str] = None
    billed_to_legal_name: Optional[str] = None
    invoice_charge_type: str
    remarks: str
    hsn_code: str
    original_invoice_number: Optional[str] = None
    source_created_on: Optional[date] = None
    buy_side_invoice_number: Optional[str] = None
    booking_owner_legal_entity_id: Optional[str] = None
    cgst: Optional[float] = None
    sgst: Optional[float] = None
    igst: Optional[float] = None
    aggregation_id: Optional[str] = None
    tax_type: Optional[str] = None
    aggregated_at: Optional[datetime] = None
    structure: str = Structure.GST.value
    nature_of_supply: str = NatureOfSupply.B2B.value
    gst_customer_type: str = GSTType.REGISTERED.value
    status: str = SalesInvoiceStatus.INGESTED
    verified: bool = True
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    checksum: Optional[str] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None

    def to_json(self):
        return self.model_dump()

    def mark_as_pushed(self):
        self.status = SalesInvoiceStatus.PUSHED

    def mark_as_unverified(self):
        self.verified = True

    def get_unique_identifier(self):
        return self.unique_ref_id

    def mark_as_aggregated(self, aggregation_id):
        self.status = SalesInvoiceStatus.AGGREGATED
        self.aggregated_at = datetime.now()
        self.aggregation_id = aggregation_id

    def aggregation_group_key(self):
        return self.SalesInvoiceGroupKey(
            entry_type=self.entry_type,
            tax_type=self.tax_type,
            tax_percentage=self.tax_percentage,
            hsn_code=self.hsn_code,
            posting_date=self.posting_date,
        )

    def update(self, data: dict):
        self.entry_type = data.get("entry_type")
        self.order_date = data.get("order_date")
        self.posting_date = data.get("posting_date")
        self.reference_number = data.get("reference_number")
        self.state_code = data.get("state_code")
        self.unit_price = data.get("unit_price")
        self.tax_percentage = data.get("tax_percentage")
        self.hotel_name = data.get("hotel_name")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.stay_days = data.get("stay_days")
        self.room_type = data.get("room_type")
        self.occupancy = data.get("occupancy")
        self.guest_name = data.get("guest_name")
        self.uvid_date = data.get("uvid_date")
        self.invoice_number = data.get("invoice_number")
        self.buy_side_invoice_number = data.get("buy_side_invoice_number")
        self.total_invoice_amount = data.get("total_invoice_amount")
        self.hotel_code = data.get("hotel_code")
        self.source = data.get("source")
        self.sub_source = get_data_hash_20(data.get("sub_source"))
        self.customer_number = data.get("customer_number")
        self.billed_to_state_code = data.get("billed_to_state_code")
        self.billed_to_gstin = data.get("billed_to_gstin")
        self.billed_to_legal_name = data.get("billed_to_legal_name")
        self.invoice_charge_type = data.get("invoice_charge_type")
        self.remarks = data.get("remarks")
        self.hsn_code = data.get("hsn_code")
        self.original_invoice_number = data.get("original_invoice_number")
        self.source_created_on = data.get("source_created_on")
        self.structure = data.get("structure")
        self.nature_of_supply = data.get("nature_of_supply")
        self.gst_customer_type = data.get("gst_customer_type")
        self.booking_owner_legal_entity_id = data.get("booking_owner_legal_entity_id")
        self.cgst = data.get("cgst")
        self.sgst = data.get("sgst")
        self.igst = data.get("igst")
        self.tax_type = data.get("tax_type")
        self.verified = True

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
