from finance_erp.common.decorators import session_manager
from finance_erp.domain.auth.entity.user import User
from finance_erp.domain.auth.models import UserModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class UserRepository(BaseRepository):
    def to_entity(self, db_model: UserModel):
        return User(email=db_model.email, authenticated=db_model.authenticated)

    def from_entity(self, domain_entity: User):
        # noinspection PyArgumentList
        return UserModel(
            email=domain_entity.email, authenticated=domain_entity.authenticated
        )

    def find(self, email):
        user = (
            self.query(UserModel).filter(
                UserModel.email == email,
                UserModel.deleted == False,
            )
        ).first()
        return self.to_entity(user) if user else None

    @session_manager(commit=True)
    def save_user(self, user: User):
        if not self.find(user.email):
            self.update(self.from_entity(user))
        return user
