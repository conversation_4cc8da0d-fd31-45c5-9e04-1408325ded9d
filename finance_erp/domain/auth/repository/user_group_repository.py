from finance_erp.common.decorators import session_manager
from finance_erp.domain.auth.entity.user_group import UserGroup
from finance_erp.domain.auth.models import UserGroupModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class UserGroupRepository(BaseRepository):
    def to_entity(self, db_model: UserGroupModel):
        return UserGroup(email=db_model.email, role_id=db_model.role_id)

    def from_entity(self, domain_entity: UserGroup):
        # noinspection PyArgumentList
        return UserGroupModel(email=domain_entity.email, role_id=domain_entity.role_id)

    def find(self, email):
        user_group = (
            self.query(UserGroupModel).filter(
                UserGroupModel.email == email,
                UserGroupModel.deleted == False,
            )
        ).first()
        return self.to_entity(user_group) if user_group else None

    @session_manager(commit=True)
    def save_user(self, user_group: UserGroup):
        if not self.find(user_group.email):
            self.update(self.from_entity(user_group))
        return user_group
