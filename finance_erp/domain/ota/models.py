from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>an, Column, Date, DateTime, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class OtaModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "ota"

    paid_at_ota = Column(DECIMAL)
    commission_amount = Column(DECIMAL)
    tds_percentage = Column(String)
    reference_number = Column(String, primary_key=True)
    hotel_code = Column(String)
    posting_date = Column(Date)
    check_in = Column(Date)
    check_out = Column(Date)
    booking_created_date = Column(Date)
    pretax_room_rent = Column(DECIMAL)
    guest_name = Column(String)
    ota_name = Column(String)
    commission_percent = Column(DECIMAL)
    mop = Column(String)

    verified = Column(Boolean, default=False)
    status = Column(String)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "posting_date": self.posting_date,
            "paid_at_ota": self.paid_at_ota,
            "commission_amount": self.commission_amount,
            "tds_percentage": self.tds_percentage,
            "reference_number": self.reference_number,
            "hotel_code": self.hotel_code,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "pretax_room_rent": self.pretax_room_rent,
            "guest_name": self.guest_name,
            "ota_name": self.ota_name,
            "commission_percent": self.commission_percent,
            "mop": self.mop,
            "booking_created_date": self.booking_created_date,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }
