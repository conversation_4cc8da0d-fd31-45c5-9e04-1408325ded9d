from finance_erp.domain.ota.entity.ota import OTACommissionEntity
from finance_erp.domain.ota.models import OtaModel
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class OtaAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: OTACommissionEntity, **kwargs):
        # noinspection PyArgumentList
        return OtaModel(
            posting_date=domain_entity.posting_date,
            paid_at_ota=domain_entity.paid_at_ota,
            commission_amount=domain_entity.commission_amount,
            tds_percentage=domain_entity.tds_percentage,
            reference_number=domain_entity.reference_number,
            hotel_code=domain_entity.hotel_code,
            check_in=domain_entity.check_in,
            check_out=domain_entity.check_out,
            pretax_room_rent=domain_entity.pretax_room_rent,
            guest_name=domain_entity.guest_name,
            ota_name=domain_entity.ota_name,
            commission_percent=domain_entity.commission_percent,
            mop=domain_entity.mop,
            booking_created_date=domain_entity.booking_created_date,
            verified=domain_entity.verified,
            status=domain_entity.status,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
        )

    def to_domain_entity(self, db_entity: OtaModel, **kwargs):
        return OTACommissionEntity(
            posting_date=db_entity.posting_date,
            paid_at_ota=db_entity.paid_at_ota,
            commission_amount=db_entity.commission_amount,
            tds_percentage=db_entity.tds_percentage,
            reference_number=db_entity.reference_number,
            hotel_code=db_entity.hotel_code,
            check_in=db_entity.check_in,
            check_out=db_entity.check_out,
            pretax_room_rent=db_entity.pretax_room_rent,
            guest_name=db_entity.guest_name,
            ota_name=db_entity.ota_name,
            commission_percent=db_entity.commission_percent,
            mop=db_entity.mop,
            booking_created_date=db_entity.booking_created_date,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
        )
