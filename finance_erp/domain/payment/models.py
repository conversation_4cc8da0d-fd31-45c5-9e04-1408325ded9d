from typing import Dict

from sqlalchemy import DE<PERSON><PERSON><PERSON>, JSON, Boolean, Column, Date, DateTime, Index, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.base import ERPEntityStatus
from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class BaseEntityModel(Base, TimeStampMixin, DeleteMixin):
    __abstract__ = True

    pg_charges = Column(DECIMAL)
    uu_id = Column(String, primary_key=True)
    pg_transaction_id = Column(String)
    reference_number = Column(String)
    hotel_code = Column(String)
    pg_tax = Column(DECIMAL)
    platform_fees = Column(DECIMAL)
    posting_date = Column(Date)
    payment_date = Column(Date)

    paid_by = Column(String)
    paid_to = Column(String)
    payment_type = Column(String)
    payment_amount = Column(DECIMAL)
    paymode = Column(String)
    paymode_type = Column(String)
    payor_entity = Column(String)
    booker_entity = Column(String)
    booking_owner = Column(JSON)
    athena_code = Column(String)
    payor_name = Column(String)
    hotel_name = Column(String)
    invoice_id = Column(String)
    check_in = Column(Date)
    check_out = Column(Date)
    channel = Column(String)
    sub_channel = Column(String)
    seller_model = Column(String)
    refund_reason = Column(String)
    original_booking_amount = Column(DECIMAL)
    is_advance = Column(Boolean, default=False)

    verified = Column(Boolean, default=False)
    last_push_attempt_at = Column(DateTime)
    erp_remarks = Column(String)


class PGPaymentStatus(ERPEntityStatus):
    AGGREGATED = "aggregated"


class PGPaymentModel(BaseEntityModel):
    __tablename__ = "pg_payments"

    status = Column(String, default=PGPaymentStatus.INGESTED)
    aggregated_at = Column(DateTime)

    uu_id = Column(String, primary_key=True)
    aggregation_id = Column(String)

    __table_args__ = (
        Index("ix_pg_payment_payment_date", "payment_date"),
        Index("ix_pg_payment_posting_date", "posting_date"),
        Index("ix_pg_payment_check_out", "check_out"),
        Index("ix_pg_payments_status", "status"),
        Index("ix_pg_payment_reference_number", "reference_number"),
        Index("ix_pg_payment_hotel_code", "hotel_code"),
        Index("ix_pg_payment_uu_id", "uu_id"),
    )

    def mapping_dict(self) -> Dict:
        mapping = {
            "pg_charges": self.pg_charges,
            "pg_transaction_id": self.pg_transaction_id,
            "reference_number": self.reference_number,
            "uu_id": self.uu_id,
            "hotel_code": self.hotel_code,
            "pg_tax": self.pg_tax,
            "platform_fees": self.platform_fees,
            "posting_date": self.posting_date,
            "payment_date": self.payment_date,
            "paid_by": self.paid_by,
            "paid_to": self.paid_to,
            "payment_type": self.payment_type,
            "payment_amount": self.payment_amount,
            "paymode": self.paymode,
            "paymode_type": self.paymode_type,
            "payor_entity": self.payor_entity,
            "booker_entity": self.booker_entity,
            "athena_code": self.athena_code,
            "payor_name": self.payor_name,
            "hotel_name": self.hotel_name,
            "invoice_id": self.invoice_id,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "channel": self.channel,
            "sub_channel": self.sub_channel,
            "seller_model": self.seller_model,
            "refund_reason": self.refund_reason,
            "original_booking_amount": self.original_booking_amount,
            "is_advance": self.is_advance,
            "verified": self.verified,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
            "aggregated_at": self.aggregated_at,
            "aggregation_id": self.aggregation_id,
            "status": self.status,
        }
        if self.booking_owner is not None:
            mapping["booking_owner"] = self.booking_owner
        return mapping


class PGPaymentSummaryModel(BaseEntityModel):
    __tablename__ = "pg_payments_summary"

    status = Column(String)

    __table_args__ = (
        Index("ix_pg_payments_summary_payment_date", "payment_date"),
        Index("ix_pg_payments_summary_posting_date", "posting_date"),
        Index("ix_pg_payments_summary_status", "status"),
        Index("ix_pg_payments_summary_reference_number", "reference_number"),
        Index("ix_pg_payments_summary_hotel_code", "hotel_code"),
    )

    def mapping_dict(self) -> Dict:
        mapping = {
            "pg_charges": self.pg_charges,
            "pg_transaction_id": self.pg_transaction_id,
            "reference_number": self.reference_number,
            "uu_id": self.uu_id,
            "hotel_code": self.hotel_code,
            "pg_tax": self.pg_tax,
            "platform_fees": self.platform_fees,
            "posting_date": self.posting_date,
            "payment_date": self.payment_date,
            "paid_by": self.paid_by,
            "paid_to": self.paid_to,
            "payment_type": self.payment_type,
            "payment_amount": self.payment_amount,
            "paymode": self.paymode,
            "paymode_type": self.paymode_type,
            "payor_entity": self.payor_entity,
            "booker_entity": self.booker_entity,
            "athena_code": self.athena_code,
            "payor_name": self.payor_name,
            "hotel_name": self.hotel_name,
            "invoice_id": self.invoice_id,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "channel": self.channel,
            "sub_channel": self.sub_channel,
            "seller_model": self.seller_model,
            "refund_reason": self.refund_reason,
            "original_booking_amount": self.original_booking_amount,
            "is_advance": self.is_advance,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
        }

        if self.booking_owner is not None:
            mapping["booking_owner"] = self.booking_owner
        return mapping
