from datetime import datetime

from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.models import PGPaymentModel
from finance_erp.domain.payment.value_objects import BookingOwner
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class PaymentAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PGPaymentEntity, **kwargs):
        # noinspection PyArgumentList
        return PGPaymentModel(
            pg_charges=domain_entity.pg_charges,
            pg_transaction_id=domain_entity.pg_transaction_id,
            uu_id=domain_entity.uu_id,
            reference_number=domain_entity.reference_number,
            hotel_code=domain_entity.hotel_code,
            pg_tax=domain_entity.pg_tax,
            platform_fees=domain_entity.platform_fees,
            posting_date=domain_entity.posting_date,
            payment_date=domain_entity.payment_date,
            paid_by=domain_entity.paid_by,
            paid_to=domain_entity.paid_to,
            payment_type=domain_entity.payment_type,
            payment_amount=domain_entity.payment_amount,
            paymode=domain_entity.paymode,
            paymode_type=domain_entity.paymode_type,
            payor_entity=domain_entity.payor_entity,
            booker_entity=domain_entity.booker_entity,
            booking_owner=domain_entity.booking_owner.to_json()
            if domain_entity.booking_owner
            else None,
            athena_code=domain_entity.athena_code,
            payor_name=domain_entity.payor_name,
            hotel_name=domain_entity.hotel_name,
            invoice_id=domain_entity.invoice_id,
            check_in=domain_entity.check_in,
            check_out=domain_entity.check_out,
            channel=domain_entity.channel,
            sub_channel=domain_entity.sub_channel,
            seller_model=domain_entity.seller_model,
            refund_reason=domain_entity.refund_reason,
            original_booking_amount=domain_entity.original_booking_amount,
            is_advance=domain_entity.is_advance,
            status=domain_entity.status,
            verified=domain_entity.verified,
            deleted=domain_entity.deleted,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            last_push_attempt_at=domain_entity.last_push_attempt_at,
            erp_remarks=domain_entity.erp_remarks,
            aggregation_id=domain_entity.aggregation_id,
            aggregated_at=domain_entity.aggregated_at,
        )

    def to_domain_entity(self, db_entity: PGPaymentModel, **kwargs):
        return PGPaymentEntity(
            pg_charges=db_entity.pg_charges,
            pg_transaction_id=db_entity.pg_transaction_id,
            reference_number=db_entity.reference_number,
            hotel_code=db_entity.hotel_code,
            pg_tax=db_entity.pg_tax,
            platform_fees=db_entity.platform_fees,
            posting_date=db_entity.posting_date,
            payment_date=db_entity.payment_date,
            paid_by=db_entity.paid_by,
            paid_to=db_entity.paid_to,
            payment_type=db_entity.payment_type,
            payment_amount=db_entity.payment_amount,
            paymode=db_entity.paymode,
            paymode_type=db_entity.paymode_type,
            payor_entity=db_entity.payor_entity,
            booker_entity=db_entity.booker_entity,
            booking_owner=BookingOwner(
                name=db_entity.booking_owner.get("name"),
                email=db_entity.booking_owner.get("email"),
                phone=db_entity.booking_owner.get("phone"),
            )
            if db_entity.booking_owner
            else None,
            athena_code=db_entity.athena_code,
            payor_name=db_entity.payor_name,
            hotel_name=db_entity.hotel_name,
            invoice_id=db_entity.invoice_id,
            check_in=db_entity.check_in,
            check_out=db_entity.check_out,
            channel=db_entity.channel,
            sub_channel=db_entity.sub_channel,
            seller_model=db_entity.seller_model,
            refund_reason=db_entity.refund_reason,
            original_booking_amount=db_entity.original_booking_amount,
            is_advance=db_entity.is_advance,
            verified=db_entity.verified,
            status=db_entity.status,
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            last_push_attempt_at=db_entity.last_push_attempt_at,
            erp_remarks=db_entity.erp_remarks,
            uu_id=db_entity.uu_id,
            aggregation_id=db_entity.aggregation_id,
            aggregated_at=db_entity.aggregated_at,
        )
