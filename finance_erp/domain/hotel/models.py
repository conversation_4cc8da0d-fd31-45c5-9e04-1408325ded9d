from typing import Dict

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class HotelModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "hotel"

    hotel_code = Column(String, primary_key=True)
    vendor_name = Column(String)
    search_name = Column(String)
    address = Column(String)
    city = Column(String)
    country_code = Column(String)
    pan = Column(String)
    state_code = Column(String)
    gstin = Column(String)
    gst_vendor_type = Column(String)
    msme = Column(String)

    verified = Column(Boolean)
    status = Column(String)
    last_push_attempt_at = Column(DateTime(timezone=True))
    erp_remarks = Column(String)
    cost_center_id = Column(String)

    def mapping_dict(self) -> Dict:
        return {
            "hotel_code": self.hotel_code,
            "vendor_name": self.vendor_name,
            "search_name": self.search_name,
            "address": self.address,
            "city": self.city,
            "country_code": self.country_code,
            "pan": self.pan,
            "state_code": self.state_code,
            "gstin": self.gstin,
            "gst_vendor_type": self.gst_vendor_type,
            "msme": self.msme,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": self.last_push_attempt_at,
            "erp_remarks": self.erp_remarks,
            "cost_center_id": self.cost_center_id,
        }
