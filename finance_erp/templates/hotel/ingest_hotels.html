{% extends 'admin/master.html' %}
{% block body %}
<style>
    .loader {
  visibility: hidden;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin: auto;
  animation: spin 2s linear infinite;
}

    @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
</head>
<body>
<h2>Sync Hotels</h2>
<div id="ManualIng">
    <div class="form-group">
        <label for="hotelIds">Hotel Ids</label>
        <input id="hotelIds" type="text" placeholder="Comma-Seperated Hotel-Ids (max 40)"  class="form-control"  aria-describedby="hotelIdsHelp">
        <small id="hotelIdsHelp" class="form-text text-muted">For Example : 0016581,0001825,.....</small>
    </div>
    <button id="AddBtn" class="btn btn-primary">Ingest</button>
</div>
<div class="loader"></div>
    <script>
            var $hotelIds = $('#hotelIds');
            $('#AddBtn').on('click',function(){
              var displayString = "Please fill ";
              var details = {
                "hotel_ids": $hotelIds.val().trim()
              }
              if(details.hotel_ids.length==0)
              {
                displayString=displayString.concat("Hotel Ids")
                alert(displayString);
                return;
              }
              if(details.hotel_ids.length>200)
              {
                displayString="Please provide string of length less than 200"
                alert(displayString);
                return;
              }
              $('.loader').css("visibility", "visible");
              fetch('/erp/api/v1/hotel-data/bulk-pull', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Application': 'FlaskAdminAPIRequest',
                        'X-Auth-Id': "{{ user_info.user_id }}",
                        'X-User-Type': "{{ user_info.role }}",
                        'X-User': "{{ user_info.user_name }}"
                    },
                    body: JSON.stringify(details)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Error Ingesting Hotel Data');
                    }
                    alert('Hotel data with ' + details.hotel_ids + ' ingested.');
                    $hotelIds.val("");
                })
                .catch(error => {
                    alert(error.message);
                })
                .finally(() => {
                    $('.loader').css("visibility", "hidden");
                });
            })
    </script>
</body>
{% endblock %}
