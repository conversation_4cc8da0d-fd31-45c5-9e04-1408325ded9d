{% extends 'admin/model/list.html' %}

{% block body %}
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
          <li class="breadcrumb-item active" aria-current="page"><h6>{{admin_view.name}}</h6></li>
      </ol>
    </nav>
    {{ super() }}
{% endblock %}

{% block model_list_table %}
    <table class="table table-striped table-bordered table-hover model-list">
        <thead>
        <tr>
            {% block list_header scoped %}
                {% if actions %}
                    <th class="list-checkbox-column">
                        <input type="checkbox" name="rowtoggle" class="action-rowtoggle"
                               title="{{ _gettext('Select all records') }}"/>
                    </th>
                {% endif %}
                {% block list_row_actions_header %}
                    {% if admin_view.column_display_actions %}
                        <th class="col-md-1">&nbsp;</th>
                    {% endif %}
                {% endblock %}
                {% set column = 0 %}
                {% for c, name in list_columns %}
                    <th class="column-header col-{{ c }}">
                        {% if admin_view.is_sortable(c) %}
                            {% if sort_column == column %}
                                <a href="{{ sort_url(column, True) }}"
                                   title="{{ _gettext('Sort by %(name)s', name=name) }}">
                                    {{ name }}
                                    {% if sort_desc %}
                                        <span class="fa fa-chevron-up glyphicon glyphicon-chevron-up"></span>
                                    {% else %}
                                        <span class="fa fa-chevron-down glyphicon glyphicon-chevron-down"></span>
                                    {% endif %}
                                </a>
                            {% else %}
                                <a href="{{ sort_url(column) }}"
                                   title="{{ _gettext('Sort by %(name)s', name=name) }}">{{ name }}</a>
                            {% endif %}
                        {% else %}
                            {{ name }}
                        {% endif %}
                        {% if admin_view.column_descriptions.get(c) %}
                            <a class="fa fa-question-circle glyphicon glyphicon-question-sign"
                               title="{{ admin_view.column_descriptions[c] }}"
                               href="javascript:void(0)" data-role="tooltip"
                            ></a>
                        {% endif %}
                    </th>
                    {% set column = column + 1 %}
                {% endfor %}
            {% endblock %}
        </tr>
        </thead>
        {% for row in data %}
            <tr class="{{ get_row_high_light_class(row) }}">
                {% block list_row scoped %}
                    {% if actions %}
                        <td>
                            <input type="checkbox" name="rowid" class="action-checkbox"
                                   value="{{ get_pk_value(row) }}" title="{{ _gettext('Select record') }}"/>
                        </td>
                    {% endif %}
                    {% block list_row_actions_column scoped %}
                        {% if admin_view.column_display_actions %}
                            <td class="list-buttons-column">
                                {% block list_row_actions scoped %}
                                    {% for action in list_row_actions %}
                                        {{ action.render_ctx(get_pk_value(row), row) }}
                                    {% endfor %}
                                {% endblock %}
                            </td>
                        {%- endif -%}
                    {% endblock %}

                    {% for c, name in list_columns %}
                        <td class="col-{{ c }}">
                            {% if admin_view.is_editable(c) %}
                                {% set form = list_forms[get_pk_value(row)] %}
                                {% if form.csrf_token %}
                                    {{ form[c](pk=get_pk_value(row), display_value=get_value(row, c), csrf=form.csrf_token._value()) }}
                                {% else %}
                                    {{ form[c](pk=get_pk_value(row), display_value=get_value(row, c)) }}
                                {% endif %}
                            {% else %}
                                {{ get_value(row, c) }}
                            {% endif %}
                        </td>
                    {% endfor %}
                {% endblock %}
            </tr>
        {% else %}
            <tr>
                <td colspan="999">
                    {% block empty_list_message %}
                        <div class="text-center">
                            {{ admin_view.get_empty_list_message() }}
                        </div>
                    {% endblock %}
                </td>
            </tr>
        {% endfor %}
    </table>
    {% block list_pager %}
        {% if num_pages is not none %}
            {{ lib.pager(page, num_pages, pager_url) }}
        {% else %}
            {{ lib.simple_pager(page, data|length == page_size, pager_url) }}
        {% endif %}
    {% endblock %}
{% endblock %}
