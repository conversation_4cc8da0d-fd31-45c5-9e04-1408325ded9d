from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.ta_commission.entity.ta_commission import (
    UPDATABLE_FIELDS,
    TACommissionEntity,
)
from finance_erp.domain.ta_commission.repository.ta_commission_data_repository import (
    TACommissionReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[TACommissionReportRepository])
class BulkUpdateTACommissionReportCommandHandler:
    def __init__(self, ta_commission_reports_repository: TACommissionReportRepository):
        self.ta_commission_reports_repository = ta_commission_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        ta_commission_report_aggregates: List[
            TACommissionEntity
        ] = self.ta_commission_reports_repository.get_by_ids(uuids)
        aggregate_map = {
            ag.reference_number: ag for ag in ta_commission_report_aggregates
        }
        ta_commission_report_aggregates = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in aggregate_map:
                raise ResourceNotFound(
                    "TACommissionRecord", extra_payload=dict(reference_number=uu_id)
                )
            ta_commission_report_aggregate: TACommissionEntity = aggregate_map[uu_id]
            if ta_commission_report_aggregate.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(pg_transaction_id=uu_id),
                )
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(ta_commission_report_aggregate, attr, request_data[attr])
            ta_commission_report_aggregates.append(ta_commission_report_aggregate)
        self.ta_commission_reports_repository.bulk_update_records(
            ta_commission_report_aggregates
        )
        return ta_commission_report_aggregates
