import uuid
from datetime import datetime

from ths_common.constants.base_enum import BaseEnum

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.date import utc_to_ist
from core.common.utils.files import write_to_csv
from finance_erp.application.payments.commands.get_paymnet_report import (
    FetchPaymentsCommandHandler,
)
from finance_erp.application.payments.dtos.get_payement_report_dto import (
    GetPaymentReportDto,
)
from finance_erp.application.purchase.command_handler.get_purchase_report import (
    FetchPurchaseReportCommandHandler,
)
from finance_erp.application.purchase.dtos.get_purchase_reprt_dto import (
    GetPurchaseReportDto,
)
from finance_erp.common.schema.payment import PGTransactionResponseSchema
from finance_erp.common.schema.purchase import PurchaseInvoiceResponseSchema
from object_registry import register_instance

TEMP_ROOT_DIR = "/tmp"
TEMP_INVOICES_REPORT_STORAGE_DIR = f"{TEMP_ROOT_DIR}/fin_app_csv"


class EntityTypes(BaseEnum):
    Payment = "payment"
    Purchase = "purchase"


@register_instance(
    dependencies=[FetchPaymentsCommandHandler, FetchPurchaseReportCommandHandler]
)
class CsvDataGenerator(TreeboBaseAPI):
    def __init__(
        self,
        payment_fetch_cmd_handler: FetchPaymentsCommandHandler,
        purchase_fetch_cmd_handler: FetchPurchaseReportCommandHandler,
    ):
        self.payment_fetch_cmd_handler = payment_fetch_cmd_handler
        self.purchase_fetch_cmd_handler = purchase_fetch_cmd_handler

    def generate(
        self, entity_type=EntityTypes.Purchase, from_date=None, to_date=None, uuids=None
    ):
        data = []
        if entity_type == EntityTypes.Payment.value:
            data = self.get_payment_data(from_date, to_date, uuids)
        elif entity_type == EntityTypes.Purchase.value:
            data = self.get_purchase_data(from_date, to_date, uuids)
        return self._generate_csv_file(entity_type, data) if data else None

    def get_payment_data(self, from_date=None, to_date=None, uuids=None):
        records = self.payment_fetch_cmd_handler.handle(
            GetPaymentReportDto(
                from_date=from_date, to_date=to_date, pg_transaction_id=uuids
            )
        )
        parsed_data = PGTransactionResponseSchema().dump(records, many=True).data
        return parsed_data

    def get_purchase_data(self, from_date=None, to_date=None, uuids=None):
        records = self.purchase_fetch_cmd_handler.handle(
            GetPurchaseReportDto(
                from_date=from_date, to_date=to_date, unique_ref_ids=uuids
            )
        )
        parsed_data = PurchaseInvoiceResponseSchema().dump(records, many=True).data
        return parsed_data

    @staticmethod
    def _generate_csv_file(entity_type, data_items):
        date = utc_to_ist(datetime.utcnow()).date().strftime("%Y-%m-%d")
        archive_file_name = f"{entity_type}/{date}/{uuid.uuid4()}.csv"
        csv_file_name = f"{TEMP_INVOICES_REPORT_STORAGE_DIR}/{archive_file_name}"
        header_row = list(data_items[0].keys())
        data_rows = [inv.values() for inv in data_items]
        data_rows = [header_row] + data_rows
        write_to_csv(file_name=csv_file_name, list_of_rows=data_rows)
        return csv_file_name
