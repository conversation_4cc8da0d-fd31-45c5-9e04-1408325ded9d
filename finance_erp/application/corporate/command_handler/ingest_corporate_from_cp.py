import logging

from thsc.crs.convertors.booking_convertors import BookingConvertor
from thsc.crs.entities.booking import Booking as THSCBooking

from finance_erp.application.common.resilient_ingestion_handler import (
    ResilientDataIngestionCommandHandler,
)
from finance_erp.common.constants import IngestionJobs
from finance_erp.domain.company_profile.dto.company_profiles_dto import SubEntity
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.factory.corporate_factory import (
    CorporateFactory,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CorporateReportRepository])
class CPCorporateDataIngestionHandler(ResilientDataIngestionCommandHandler):
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
    ):
        self.corporate_repository = corporate_repository
        super().__init__(IngestionJobs.CP_CORPORATE_INGESTION)

    def _execute(self, request_data, event_id, from_async_job=False):
        sub_entity = SubEntity(**request_data)
        return self._ingest(sub_entity)

    def _ingest(self, sub_entity: SubEntity):
        existing_corporate: CorporateEntity = self.corporate_repository.get(
            sub_entity.superhero_company_code
        )
        if sub_entity.is_test:
            logger.info(
                f"Corporate is test with id: {existing_corporate.corporate_code}"
            )
            return
        new_entity = CorporateFactory.create_from_company_profile_entity(sub_entity)
        if existing_corporate:
            logger.info(
                f"Corporate already exists with id: {existing_corporate.corporate_code}"
            )
            existing_corporate.update(new_entity)
            return self.corporate_repository.update(existing_corporate)

        return self.corporate_repository.save(new_entity)

    @staticmethod
    def _get_thsc_booking(request_data, re_fetch_invoice) -> THSCBooking:
        if re_fetch_invoice:
            return THSCBooking.get(request_data.get("booking_id"))
        return BookingConvertor().from_dict(request_data)
