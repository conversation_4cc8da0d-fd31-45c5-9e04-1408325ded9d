from datetime import datetime, timedelta

from core.common.utils.date import utc_to_ist
from finance_erp.application.settlement.tax.dtos.generate_tax_report_dto import (
    GenerateTaxReportDto,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        CrsServiceClient,
    ]
)
class TaxReportGeneratorCommandHandler:
    def __init__(self, crs_service_client: CrsServiceClient):
        self.crs_service_client = crs_service_client
        pass

    def handle(self, input_criteria: GenerateTaxReportDto):
        if not input_criteria.date:
            input_criteria.date = (
                utc_to_ist(datetime.utcnow() - timedelta(days=1))
                .date()
                .strftime("%Y-%m-%d")
            )
        # TODO enhance crs reports to generate row wise
        self.crs_service_client.trigger_async_report_generation(
            date=input_criteria.date, report_name=NavisionReports.TAX_REPORT
        )
        return f"Report generation triggered successfully"
