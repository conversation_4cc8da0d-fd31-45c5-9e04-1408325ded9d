from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.settlement.treebo_fee.dtos.get_treebo_fee_report_dto import (
    GetTreeboFeeReportDto,
)
from finance_erp.domain.settlement.treebo_fee.repository.treebo_fee_repository import (
    TreeboFeeRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[TreeboFeeRepository])
class FetchTreeboFeeCommandHandler:
    def __init__(self, settlement_treebo_fee_reports_repository: TreeboFeeRepository):
        self.settlement_treebo_fee_reports_repository = (
            settlement_treebo_fee_reports_repository
        )

    def handle(self, input_criteria: GetTreeboFeeReportDto):
        if input_criteria.uu_ids:
            input_criteria.uu_ids = (
                input_criteria.uu_ids.split(",") if input_criteria.uu_ids else None
            )
            return self.settlement_treebo_fee_reports_repository.get_by_ids(
                input_criteria.uu_ids
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return (
                self.settlement_treebo_fee_reports_repository.get_reports_by_date_range(
                    from_date=input_criteria.from_date, to_date=input_criteria.to_date
                )
            )
