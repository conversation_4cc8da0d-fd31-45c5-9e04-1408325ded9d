from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.settlement.loan.entity.loan import UPDATABLE_FIELDS, LoanEntity
from finance_erp.domain.settlement.loan.repository.loan_repository import LoanRepository
from object_registry import register_instance


@register_instance(dependencies=[LoanRepository])
class BulkUpdateLoanReportCommandHandler:
    def __init__(self, settlement_loan_reports_repository: LoanRepository):
        self.settlement_loan_reports_repository = settlement_loan_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        settlement_loan_report_reports: List[
            LoanEntity
        ] = self.settlement_loan_reports_repository.get_by_ids(uuids, for_update=True)
        report_map = {ag.uu_id: ag for ag in settlement_loan_report_reports}
        settlement_loan_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["uu_id"]
            if uu_id not in report_map:
                raise ResourceNotFound("LoanRecord", extra_payload=dict(uu_id=uu_id))
            settlement_loan_report_report: LoanEntity = report_map[uu_id]
            if settlement_loan_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(uu_id=uu_id),
                )
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(settlement_loan_report_report, attr, request_data[attr])
            settlement_loan_report_reports.append(settlement_loan_report_report)
        self.settlement_loan_reports_repository.bulk_update_records(
            settlement_loan_report_reports
        )
        return settlement_loan_report_reports
