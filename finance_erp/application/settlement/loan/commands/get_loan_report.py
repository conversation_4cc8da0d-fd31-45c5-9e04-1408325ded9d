from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.settlement.loan.dtos.get_loan_report_dto import (
    GetLoanReportDto,
)
from finance_erp.domain.settlement.loan.repository.loan_repository import LoanRepository
from object_registry import register_instance


@register_instance(dependencies=[LoanRepository])
class FetchLoanCommandHandler:
    def __init__(self, settlement_loan_reports_repository: LoanRepository):
        self.settlement_loan_reports_repository = settlement_loan_reports_repository

    def handle(self, input_criteria: GetLoanReportDto):
        if input_criteria.uu_ids:
            input_criteria.uu_ids = (
                input_criteria.uu_ids.split(",") if input_criteria.uu_ids else None
            )
            return self.settlement_loan_reports_repository.get_by_ids(
                input_criteria.uu_ids
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.settlement_loan_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
