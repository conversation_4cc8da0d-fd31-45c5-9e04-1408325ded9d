import logging
import traceback
from itertools import chain

from ths_common.exceptions import ValidationException
from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.application.common.dtos.data_push_base_dto import DataPushBaseDto
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.common.exception import NavisionDuplicateException
from finance_erp.common.utils.utils import partition
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        NavPushAuditService,
        HotelReportRepository,
        TenantSettings,
    ]
)
class BaseDataPushCommandHandler:
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        nav_push_audit_service: NavPushAuditService,
        hotel_reports_repository: HotelReportRepository,
        tenant_settings: TenantSettings,
    ):
        self.external_data_push_client: BusinessCentralClient = (
            external_data_push_client
        )
        self.slack_alert_client: SlackAlertServiceClient = slack_alert_client
        self.nav_push_audit_service: NavPushAuditService = nav_push_audit_service
        self.hotel_reports_repository: HotelReportRepository = hotel_reports_repository
        self.tenant_settings: TenantSettings = tenant_settings

    def handle(self, request_dto, report_name, event_id=None):
        try:
            is_retry = event_id is not None
            event_id = event_id if event_id else get_current_request_id()
            if is_retry:
                self.nav_push_audit_service.record_data_push_retry_event(
                    event_id, report_name, stats=vars(request_dto)
                )
            else:
                self.nav_push_audit_service.record_incoming_data_push_event(
                    event_id, report_name, stats=vars(request_dto)
                )
            return self._run_data_push(request_dto, report_name, event_id)
        except Exception as e:
            logger.exception(e)
            message = f"{report_name} Data push failed: {str(e)}"
            self.nav_push_audit_service.record_data_push_failure_event(
                event_id, report_name, message, str(traceback.format_exc())
            )
            raise  # if its an async req, raising the exception is enough, will be retried by the JOB framework

    def _run_data_push(self, request_dto, report_name, event_id):
        records = self._get_records_to_push(request_dto)
        records = self._apply_data_exclusion_rules(records)
        records_after_processing = self._process_records(records)
        self._validate_records(records_after_processing)
        self._record_data_push_started_event(
            event_id, records, records_after_processing, report_name
        )
        failures = []
        success = []
        duplicate_pushes = []
        for record in records:
            try:
                self._parse_and_push_record_to_external_service_provider(
                    record, report_name
                )
                self._capture_success(record)
                success.append(record.get_unique_identifier())
            except NavisionDuplicateException:
                # Duplicate error means record is already there in navision.
                # ideally this will never happen: revisit later found any
                duplicate_pushes.append(record.get_unique_identifier())
                self._capture_success(record)
                # TODO add slack alert
            except Exception as e:
                reason = str(e)
                self._capture_failure(record, reason)
                failures.append(
                    dict(uuid=record.get_unique_identifier(), reason=reason)
                )
        stats = dict(
            failures=failures,
            success=success,
            duplicate_pushes=duplicate_pushes,
            failure_count=len(failures),
            success_count=len(success),
            duplicate_count=len(duplicate_pushes),
        )
        self.nav_push_audit_service.record_data_push_completed_event(
            event_id, report_name, stats=stats
        )
        if failures:
            self._alert_failures_in_slack(event_id, report_name, failures)
        return stats

    def _alert_failures_in_slack(self, event_id, report_name, failures):
        process_name = f"Data push {report_name}"
        self.slack_alert_client.record_event(
            "data_push_failures",
            dict(process_name=process_name, req_id=event_id, failures=failures),
        )

    def _record_data_push_started_event(
        self, event_id, records, records_after_processing, report_name
    ):
        uuid_of_records_to_be_pushed = [
            record.get_unique_identifier() for record in records
        ]
        uuid_of_records_after_processing = [
            record.get_unique_identifier() for record in records_after_processing
        ]
        stats = dict(
            records_to_be_pushed=uuid_of_records_to_be_pushed,
            no_of_records_to_be_pushed=len(uuid_of_records_to_be_pushed),
        )
        if len(uuid_of_records_to_be_pushed) != len(uuid_of_records_after_processing):
            stats.update(
                records_to_be_pushed_after_processing=uuid_of_records_after_processing,
                no_of_records_to_be_pushed_after_processing=len(
                    uuid_of_records_after_processing
                ),
            )
        self.nav_push_audit_service.record_data_push_started_event(
            event_id, report_name, stats=stats
        )

    def _get_records_to_push(self, request_dto: DataPushBaseDto):
        raise NotImplementedError()

    def _apply_data_exclusion_rules(self, records):
        black_listed_hotels = (
            self.tenant_settings.get_business_central_blacklisted_hotels()
        )
        excluded_records, records_to_push = partition(
            records, lambda item: item.hotel_code in black_listed_hotels
        )
        unique_hotel_codes = list({record.hotel_code for record in records_to_push})
        hotels = self.hotel_reports_repository.get_by_ids(unique_hotel_codes)
        hotels_with_missing_cc_id = [
            hotel.hotel_code for hotel in hotels if not hotel.cost_center_id
        ]
        excluded_missing_cc_id, records_to_push = partition(
            records_to_push, lambda item: item.hotel_code in hotels_with_missing_cc_id
        )
        records_to_update = []
        for record in chain(excluded_records, excluded_missing_cc_id):
            if record in excluded_missing_cc_id:
                reason = "Not pushed due to missing Cost Centre ID"
                record.capture_data_push_failure(reason)
            else:
                reason = f"This Hotel ({record.hotel_code}) is excluded"
                record.mark_as_excluded_for_data_push(reason)
            records_to_update.append(record)
        if records_to_update:
            self._update_records(records_to_update)
        hotel_cc_mapping = {
            hotel.hotel_code: hotel.cost_center_id
            for hotel in hotels
            if hotel.cost_center_id
        }
        for record in records_to_push:
            if not record.cost_center_id and record.hotel_code in hotel_cc_mapping:
                record.cost_center_id = hotel_cc_mapping[record.hotel_code]
        return records_to_push

    # if needed concrete class can override this function to process records
    def _process_records(self, records):
        return records

    # override this method in child class if needed
    def _update_records(self, records):
        pass

    @staticmethod
    def _validate_records(records):
        for record in records:
            if record.pushed:
                raise ValidationException(
                    ApplicationErrors.DATA_PUSH_NOT_ALLOWED_FOR_ALREADY_PUSHED_RECORDS,
                    extra_payload=dict(uuid=record.get_unique_identifier()),
                )
            elif not record.verified:
                raise ValidationException(
                    ApplicationErrors.DATA_PUSH_NOT_ALLOWED_FOR_UNVERIFIED_RECORDS,
                    extra_payload=dict(uuid=record.get_unique_identifier()),
                )

    def _parse_and_push_record_to_external_service_provider(self, record, report_name):
        data_to_push = self._parse_data(record)
        self.external_data_push_client.publish(data_to_push, report_name)

    def _parse_data(self, record):
        schema, json_key = self._get_schema_and_json_key()
        data = schema(**record.to_json()).dump_with_empty_strings()
        return {DataPushNavKeys.REQUEST_OUTER_WRAPPER_KEY: data}

    def _get_schema_and_json_key(self):
        raise NotImplementedError()

    def _capture_failure(self, record, reason):
        raise NotImplementedError()

    def _capture_success(self, record):
        raise NotImplementedError()

    def _get_request_parser(self):
        raise NotImplementedError()

    def handle_request_from_job_executor(
        self, request_data, report_name, event_id=None, **kwargs
    ):
        request_dto = self._get_request_parser()(**request_data)
        request_dto.is_manual_push = False
        self.handle(request_dto, report_name, event_id)

    @staticmethod
    @session_manager(commit=True)
    def handle_async_execution_request(request_dto, report_name, event_id=None):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_push_job(
            report_name, request_dto, event_id
        )
        return dict(job_id=job.job_id)
