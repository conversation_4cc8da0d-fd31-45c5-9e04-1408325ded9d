import logging
import uuid
from pathlib import Path
from typing import List

from ths_common.value_objects import EmailAttachment

from finance_erp.application.back_office.comand_handlers.update_transaction_master import (
    UpdateTransactionMasterCommandHandler,
)
from finance_erp.application.back_office.dtos.transaction_master_dto import (
    TransactionMasterDto,
)
from finance_erp.application.hotel_settings.configs.payment_debto_mapping import (
    PaymentDebtorCodeMappingDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.common.constants import LedgersFileType
from finance_erp.common.utils.s3_file_archiver import (
    InputFileMetaForFileArchiver,
    S3FileArchiver,
)
from finance_erp.domain.back_office.constants import (
    AccountMasterTypes,
    IntegratedERPs,
    LedgerFileConstants,
)
from finance_erp.domain.back_office.core.prologic.ledger_generator import (
    DayEndLedgerDataGenerator,
)
from finance_erp.domain.back_office.entity.config_master import (
    AccountMaster,
    ConfigMaster,
    CorporateAccount,
)
from finance_erp.domain.back_office.entity.ledger_controls import (
    CLLedgerItem,
    GLLedgerItem,
    LedgerControls,
)
from finance_erp.domain.back_office.entity.ledgers_file import LedgersFileEntity
from finance_erp.domain.back_office.repository.cl_control_repository import (
    CLControlRepository,
)
from finance_erp.domain.back_office.repository.gl_control_repository import (
    GLControlRepository,
)
from finance_erp.domain.back_office.repository.ledgers_file import LedgersFileRepository
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from finance_erp.infrastructure.external_clients.notification_service_client import (
    NotificationSenderEmail,
    NotificationSenderName,
    NotificationServiceClient,
)
from object_registry import register_instance

TMP_FOLDER = "/tmp"
EXPIRY_TIME = 1 * 3600
APPLICATION_NAME = "fin-erp"

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        LedgersFileRepository,
        CorporateReportRepository,
        UpdateTransactionMasterCommandHandler,
        DayEndLedgerDataGenerator,
        TenantSettings,
        TransactionMasterRepository,
        CLControlRepository,
        GLControlRepository,
        NotificationServiceClient,
        JobRegistry,
    ]
)
class DailyLedgerCreationCommandHandler:
    def __init__(
        self,
        ledgers_file_repository: LedgersFileRepository,
        corporate_data_repository: CorporateReportRepository,
        transaction_master_updater: UpdateTransactionMasterCommandHandler,
        ledger_data_generator: DayEndLedgerDataGenerator,
        tenant_settings: TenantSettings,
        transaction_master_repository: TransactionMasterRepository,
        cl_control_repository: CLControlRepository,
        gl_control_repository: GLControlRepository,
        notification_service_client: NotificationServiceClient,
        job_registry: JobRegistry,
    ):
        self.ledgers_file_repository = ledgers_file_repository
        self.corporate_data_repository = corporate_data_repository
        self.transaction_master_updater = transaction_master_updater
        self.ledger_data_generator = ledger_data_generator
        self.transaction_master_repository = transaction_master_repository
        self.cl_control_repository = cl_control_repository
        self.gl_control_repository = gl_control_repository
        self.notification_service_client = notification_service_client
        self.tenant_settings = tenant_settings

        job_registry.register(JobName.BACKOFFICE_LEDGER_FILE_GENERATION, self.handle)

    def handle(
        self,
        date,
        hotel_id,
        refresh_transaction_master=True,
        erp_name=IntegratedERPs.PROLOGIC,
    ):
        self._remove_any_existing_data(date, hotel_id, erp_name)

        if refresh_transaction_master:
            txn_master_dto_query = TransactionMasterDto(
                hotel_id=hotel_id, erp_name=erp_name
            )
            txn_master_codes = self.transaction_master_updater.handle(
                txn_master_dto_query
            )
        else:
            txn_master_codes = self.transaction_master_repository.load_all(
                hotel_id, erp_name=erp_name, is_active=True
            )

        account_masters: List[AccountMaster] = self.get_account_masters(hotel_id)
        receive_later_payment_modes = (
            self.tenant_settings.get_receive_later_payment_modes(
                hotel_id,
            )
        )
        unit_credit_payment_modes = self.tenant_settings.get_units_credit_pay_modes(
            hotel_id,
        )
        refund_modes_to_exclude = (
            self.tenant_settings.get_refund_exclusion_list_for_backoffice(
                hotel_id,
            )
        )
        payment_config = self.tenant_settings.get_payment_config(
            hotel_id,
        )
        hotel_code = self.tenant_settings.get_backoffice_hotel_code(
            hotel_id,
        )
        config: ConfigMaster = ConfigMaster(
            hotel_id=hotel_id,
            account_master=account_masters,
            txn_master_codes=txn_master_codes,
            receive_later_payment_modes=receive_later_payment_modes,
            credit_payment_modes=unit_credit_payment_modes,
            refund_modes_to_exclude=refund_modes_to_exclude,
            payment_config=payment_config,
        )
        ledger_controls: LedgerControls = self.ledger_data_generator.generate(
            date, hotel_id, config
        )
        ledger_controls.populate_txn_entries_with_missing_gl_codes(txn_master_codes)
        ledger_controls.enrich_erp_name(erp_name)
        tmp_storage_directory = self.prepare_tmp_data_folder()
        gl_file = self.generate_gl_ledger_file(
            date,
            hotel_id,
            ledger_controls.gl_controls,
            tmp_storage_directory,
            erp_name,
            hotel_code,
        )
        cl_file = self.generate_cl_ledger_file(
            date,
            hotel_id,
            ledger_controls.cl_controls,
            tmp_storage_directory,
            erp_name,
            hotel_code,
        )
        archive_file = self.archive_ledger_files(
            date, hotel_id, [gl_file, cl_file], erp_name
        )

        if ledger_controls.has_errors():
            self.generate_and_send_error_logs(
                date, hotel_id, ledger_controls, tmp_storage_directory, hotel_code
            )

        if ledger_controls.cl_controls:
            self.cl_control_repository.insert_many(ledger_controls.cl_controls)
        if ledger_controls.gl_controls:
            self.gl_control_repository.insert_many(ledger_controls.gl_controls)

        files = [
            gl_file,
            cl_file,
            archive_file,
        ]

        self.ledgers_file_repository.insert_many(files)
        return files

    def _remove_any_existing_data(self, date, hotel_id, erp_name):
        exiting_ledgers = self.ledgers_file_repository.fetch(
            hotel_id, date, erp_name=erp_name
        )
        if exiting_ledgers:
            for ledger in exiting_ledgers:
                ledger.mark_as_deleted()
            self.ledgers_file_repository.update_many(exiting_ledgers)

        exiting_cl_controls = self.cl_control_repository.fetch(hotel_id, date, erp_name)
        if exiting_cl_controls:
            for control in exiting_cl_controls:
                control.mark_as_deleted()
            self.cl_control_repository.update_many(exiting_cl_controls)

        exiting_gl_controls = self.gl_control_repository.fetch(hotel_id, date, erp_name)
        if exiting_gl_controls:
            for control in exiting_gl_controls:
                control.mark_as_deleted()
            self.gl_control_repository.update_many(exiting_gl_controls)

    @staticmethod
    def prepare_tmp_data_folder():
        folder_path = Path(f"{TMP_FOLDER}/{uuid.uuid4()}")
        folder_path.mkdir(parents=True, exist_ok=True)
        return folder_path

    @staticmethod
    def generate_gl_ledger_file(
        date,
        hotel_id,
        gl_controls: List[GLLedgerItem],
        folder_path,
        erp_name,
        hotel_code,
    ):
        gl_controls = sorted(gl_controls, key=lambda x: x.txn_group)
        formatted_date = date.strftime(LedgerFileConstants.DATE_FORMAT)
        file_name = (
            f"{LedgerFileConstants.GUEST_LEDGER_FILE_PREFIX}"
            f"{date.strftime(LedgerFileConstants.DATE_FORMAT)}{hotel_code}.txt"
        )
        file_path = folder_path / file_name
        s3_path = (
            f"{APPLICATION_NAME}/{hotel_id}/{erp_name}/{formatted_date}/"
            f"{LedgerFileConstants.GUEST_LEDGER_FILE_PREFIX}/"
        )

        with open(file_path, "w") as file:
            file.writelines(
                f"{control.data_repr}{LedgerFileConstants.RECORD_SEPARATOR}"
                for control in gl_controls
            )

        s3_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            s3_path,
            str(file_path),
            EXPIRY_TIME,
        )

        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.GUEST_LEDGER_FILE,
            ledger_file_path=s3_signed_url,
            ledger_file_name=file_name,
        )

    @staticmethod
    def generate_cl_ledger_file(
        date,
        hotel_id,
        cl_controls: List[CLLedgerItem],
        folder_path,
        erp_name,
        hotel_code,
    ):
        formatted_date = date.strftime(LedgerFileConstants.DATE_FORMAT)
        file_name = (
            f"{LedgerFileConstants.CITY_LEDGER_FILE_PREFIX}"
            f"{formatted_date}{hotel_code}.txt"
        )
        file_path = folder_path / file_name
        s3_path = (
            f"{APPLICATION_NAME}/{hotel_id}/{erp_name}/{formatted_date}/"
            f"{LedgerFileConstants.CITY_LEDGER_FILE_PREFIX}/"
        )

        with open(file_path, "w") as file:
            file.writelines(
                f"{control.data_repr}{LedgerFileConstants.RECORD_SEPARATOR}"
                for control in cl_controls
            )

        s3_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            s3_path,
            str(file_path),
            EXPIRY_TIME,
        )
        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.CITY_LEDGER_FILE,
            ledger_file_path=s3_signed_url,
            ledger_file_name=file_name,
        )

    @staticmethod
    def archive_ledger_files(
        date, hotel_id, ledger_files: List[LedgersFileEntity], erp_name
    ):
        file_archiver = S3FileArchiver(f"{erp_name}_finance_ledgers")
        for ledger_file in ledger_files:
            file_archiver.download_file(
                InputFileMetaForFileArchiver(
                    file_name=ledger_file.ledger_file_name,
                    file_url=ledger_file.ledger_file_path,
                )
            )
        archive_file = file_archiver.prepare_archive_and_upload()
        return LedgersFileEntity(
            hotel_id=hotel_id,
            erp_name=erp_name,
            business_date=date,
            ledger_file_type=LedgersFileType.CONSOLIDATED_LEDGER_ARCHIVE,
            ledger_file_path=archive_file.signed_url,
            ledger_file_name=archive_file.file_name,
        )

    def generate_and_send_error_logs(
        self,
        date,
        hotel_id,
        ledger_controls: LedgerControls,
        folder_path,
        hotel_code,
    ):
        hotel_pocs_email = self.tenant_settings.get_hotel_pocs_email(hotel_id)
        if not hotel_pocs_email:
            logger.error(f"No hotel POCs email found for hotel_id: {hotel_id}")
            return

        formatted_date = date.strftime(LedgerFileConstants.DATE_FORMAT)
        attachments = []

        def generate_log_file(file_prefix, content):
            file_name = f"{file_prefix}{formatted_date}{hotel_code}.csv"
            file_path = folder_path / file_name
            s3_path = f"{APPLICATION_NAME}/{hotel_id}/{formatted_date}/{file_prefix}/"

            with open(file_path, "w") as file:
                file.write(
                    "Issue Type,Identifier,Identifier Type,Reason,Other Details\n"
                )
                for line in content:
                    # This can be removed after testing
                    logger.info(f"Logs of Ledger generation: {line.data_repr}")
                    file.write(f"{line.data_repr}\n")

            url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                s3_path, str(file_path), EXPIRY_TIME
            )
            return EmailAttachment(filename=file_name, url=url)

        if ledger_controls.missing_txn_master:
            attachments.append(
                generate_log_file(
                    "MissingTxnMaster", list(ledger_controls.missing_txn_master)
                )
            )

        if ledger_controls.skipped_transactions:
            attachments.append(
                generate_log_file(
                    "SkippedTransactions", ledger_controls.skipped_transactions
                )
            )

        if attachments:
            self.notification_service_client.email(
                body_html="Please find the attached error logs for ledger file generation",
                subject=f"Ledger Generation Error Logs : {formatted_date} : {hotel_id}",
                sender_name=NotificationSenderName.TREEBO_HOTELS,
                sender=NotificationSenderEmail.TREEBO_HOTELS,
                recievers=hotel_pocs_email,
                attachments=attachments,
            )
            logger.info(f"Error logs sent to hotel POCs for hotel_id: {hotel_id}")

    def get_account_masters(self, hotel_id):
        payment_account_mapping: List[
            PaymentDebtorCodeMappingDto
        ] = self.tenant_settings.get_payment_debtor_mapping(hotel_id)

        fall_back_account_number = self.tenant_settings.get_bo_fallback_account_number(
            hotel_id
        )

        account_master = []
        for mapping in payment_account_mapping:
            account_master.append(
                AccountMaster(
                    account=CorporateAccount(
                        debtor_code=mapping.account_number,
                        company_name=mapping.identifier,
                        account_number=mapping.account_number,
                    ),
                    identifier=mapping.identifier,
                )
            )
        if fall_back_account_number:
            account_master.append(
                AccountMaster(
                    account=CorporateAccount(
                        debtor_code=fall_back_account_number,
                        company_name=AccountMasterTypes.FALL_BACK_ACCOUNT,
                        account_number=fall_back_account_number,
                    ),
                    identifier=AccountMasterTypes.FALL_BACK_ACCOUNT,
                )
            )
        return account_master
