class TenantConstants:
    CORPORATE_COMM_CHANNEL_CONFIG = "finance_erp.corporate_comm_channel_config"
    CREDIT_NOTE_COMM_ENABLED = "finance_erp.credit_note_comm_enabled"
    INCLUDE_CREDIT_NOTE_IN_STAY_SUMMARY = (
        "finance_erp.include_credit_note_in_stay_summary"
    )
    B2B_SCRIPT_RECEIVER_EMAILS = "finance_erp.b2b_script_receiver_emails"
    BILLING_TEAM_CONTACT = "finance_erp.billing_team_contact"
    CHAIN_MANGER_COMPANY_DETAILS = "finance_erp.chain_manager_company_details"
    CHAIN_MANGER_BANK_DETAILS = "finance_erp.chain_manager_bank_details"
    PAYMENT_DEBTOR_MAPPING = "finance_erp.payment_debtor_mapping"
    FALL_BACK_ACCOUNT_NUMBER = "finance_erp.fall_back_account_number"
    RECEIVE_LATER_PAYMENT_MODES = "finance_erp.receive_later_payment_modes"
    UNITS_CREDIT_PAYMENT_MODES = "finance_erp.units_credit_payment_modes"
    BACKOFFICE_REFUND_EXCLUSION_LIST = "finance_erp.backoffice_refund_exclusion_list"
    HOTEL_POCS_EMAIL = "finance_erp.hotel_pocs_email"
    ENABLED_BACKOFFICE_ERPS = "finance_erp.enabled_backoffice_erps"
    PAYMENT_CONFIG = "payment_config"
    BACKOFFICE_HOTEL_CODE = "finance_erp.backoffice_hotel_code"
    BUSINESS_CENTRAL_BLACKLISTED_HOTELS = (
        "finance_erp.business_central_blacklisted_hotels"
    )


class TenantSettingsDefaultValues:
    B2B_SCRIPT_RECEIVER_EMAILS = [
        "<EMAIL>",
    ]
    DEV_EMAILS = []
    BILLING_TEAM_CONTACT = {
        "email": "<EMAIL>",
        "name": "Treebo Billing",
    }
