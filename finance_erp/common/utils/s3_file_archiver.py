import logging
import os
import shutil
import uuid
from pathlib import Path

from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)

EXPIRY_TIME = 24 * 7 * 3600
APPLICATION_NAME = "fin-erp"
logger = logging.getLogger(__name__)


class FileMeta:
    def __init__(self, signed_url, file_size, file_name):
        self.signed_url = signed_url
        self.file_size = file_size
        self.file_name = file_name


class InputFileMetaForFileArchiver:
    def __init__(self, file_url, file_name):
        self.file_url = file_url
        self.file_name = file_name


class S3FileArchiver:
    """
    This class has util methods to download file from s3 and zipping.
    """

    def __init__(
        self,
        file_type: str,
        expiry_time: int = EXPIRY_TIME,
    ):
        base_storage_dir = (
            f'{os.environ.get("TEMPORARY_DIRECTORY", "/tmp/")}/{file_type}'
        )

        self.archive_s3_path = f"{APPLICATION_NAME}-{file_type}/"
        self.expiry_time = expiry_time
        self.temp_storage_dir = f"{base_storage_dir}/{uuid.uuid4()}"
        self.archive_path = f"{base_storage_dir}/{uuid.uuid4()}_{file_type}"
        self._setup_invoices_storage_dir()

    def download_file(self, file_meta: InputFileMetaForFileArchiver):
        with open(
            f"{self.temp_storage_dir}/{file_meta.file_name}", "wb"
        ) as file_object:
            AwsServiceClient.download_file_from_s3(file_meta.file_url, file_object)

    def prepare_archive_and_upload(self) -> FileMeta:
        shutil.make_archive(
            self.archive_path,
            "zip",
            self.temp_storage_dir,
        )
        file_path = f"{self.archive_path}.zip"
        pre_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            self.archive_s3_path,
            file_path,
            self.expiry_time,
        )
        file_size_in_mb = (
            os.path.getsize(file_path) / 1000**2
        )  # lets consider in decimal not binary
        file_name = Path(file_path).name
        self._teardown_invoices_storage_dir()
        return FileMeta(
            signed_url=pre_signed_url, file_size=file_size_in_mb, file_name=file_name
        )

    def _setup_invoices_storage_dir(self):
        if os.path.exists(self.temp_storage_dir):
            shutil.rmtree(self.temp_storage_dir)
        os.makedirs(self.temp_storage_dir)

    def _teardown_invoices_storage_dir(self):
        try:
            shutil.rmtree(self.temp_storage_dir)
            os.remove(f"{self.archive_path}.zip")
        except FileNotFoundError:
            logger.info(f"No temp storage directory found")
