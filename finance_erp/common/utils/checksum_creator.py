import hashlib

from finance_erp.common.constants import NavisionReports

CHECKSUM_ATTR_MAP = {
    NavisionReports.HOTEL_REPORT: (
        "vendor_name",
        "search_name",
        "address",
        "city",
        "country_code",
        "pan",
        "state_code",
        "gstin",
        "gst_vendor_type",
        "msme",
        "cost_center_id",
    ),
    NavisionReports.CORPORATE_REPORT: (
        "customer_legal_name",
        "customer_trading_name",
        "address",
        "city",
        "phone_number",
        "post_code",
        "email",
        "credit_limit",
        "country_code",
        "pan",
        "state_code",
        "gstin",
        "gst_customer_type",
        "tan_number",
    ),
}


def generate_check_sum(report_name, data_class=None, data_dict=None):
    if data_class:
        data_to_hash = "".join(
            [
                getattr(data_class, attr) or ""
                for attr in CHECKSUM_ATTR_MAP.get(report_name)
            ]
        )
    else:
        data_to_hash = "".join(
            [data_dict.get(attr) or "" for attr in CHECKSUM_ATTR_MAP.get(report_name)]
        )

    return hashlib.md5(data_to_hash.encode("utf-8")).hexdigest()


def get_data_hash_20(data):
    if data:
        return hashlib.sha256(data.encode("utf-8")).hexdigest()[:20]
    return ""
