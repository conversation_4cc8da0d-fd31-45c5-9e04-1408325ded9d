from ths_common.exceptions import CRSError


class ApplicationErrors(CRSError):
    DATA_PUSH_NOT_ALLOWED_FOR_ALREADY_PUSHED_RECORDS = (
        "1001",
        "Cannot push already pushed records",
    )
    DATA_PUSH_NOT_ALLOWED_FOR_UNVERIFIED_RECORDS = (
        "1002",
        "Cannot push unverified records",
    )
    CANNOT_UPDATE_PUSHED_RECORDS = ("1003", "Cannot update pushed records")
    INVALID_BACKOFFICE_ERP = (
        "1004",
        "Invalid backoffice ERP Name/Please check tenant configuration",
    )
