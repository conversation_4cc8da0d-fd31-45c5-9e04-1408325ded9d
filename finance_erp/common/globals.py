import threading


class FinanceErpContext(threading.local):
    def __init__(self):
        super(FinanceErpContext, self).__init__()
        self.business_central_context = None

    def set_business_central_context(self, business_central_context):
        self.business_central_context = business_central_context

    def clear(self):
        self.business_central_context = None


finance_erp_context = FinanceErpContext()
consumer_context = FinanceErpContext()
