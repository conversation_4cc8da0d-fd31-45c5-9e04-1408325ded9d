from datetime import date, datetime
from typing import List, Optional

from pydantic import BaseModel


class BaseFinancialSchema(BaseModel):
    uu_id: Optional[str] = None
    fin_erp_posting_date: Optional[date] = None
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None


class BaseRequestSchema(BaseFinancialSchema):
    bill_id: str
    hotel_id: str
    booking_id: Optional[str] = None
    category: Optional[str] = None
    owner_name: Optional[str] = None
    folio_number: Optional[int] = None
    checkin_date: Optional[date] = None
    checkout_date: Optional[date] = None
    billed_entity_id: Optional[int] = None
    account_number: Optional[int] = None
    revenue_center: Optional[str] = None
    booking_reference_number: Optional[str] = None


class TaxDetailSchema(BaseModel):
    amount: float
    percentage: str
    tax_type: str


class PaymentSplitDetailSchema(BaseModel):
    amount: float
    payment_id: int
    payment_split_id: int


class PaymentRequestSchema(BaseRequestSchema):
    payment_id: Optional[int] = None
    payment_split_id: Optional[int] = None
    payment_type: Optional[str] = None
    amount: str
    posting_date: Optional[date] = None
    date_of_payment: Optional[date] = None
    payment_mode: Optional[str] = None
    crs_payment_mode: Optional[str] = None
    crs_payment_mode_sub_type: Optional[str] = None
    debtor_code: Optional[str] = None
    payment_mode_sub_type: Optional[str] = None
    payment_ref_id: Optional[str] = None
    payment_channel: Optional[str] = None
    hotel_id: str  # Redundant, but keeping to match original
    room_number: Optional[str] = None


class ChargeRequestSchema(BaseRequestSchema):
    charge_id: int
    charge_split_id: int
    pretax_amount: float
    posttax_amount: float
    tax_amount: float
    tax_details: List[TaxDetailSchema]
    charge_type: Optional[str] = None
    posting_date: str
    applicable_business_date: Optional[str] = None
    bill_to_type: Optional[str] = None
    sku_category_id: Optional[str] = None
    is_inclusion_charge: Optional[bool] = None
    item_id: Optional[str] = None


class AllowanceRequestSchema(BaseRequestSchema):
    allowance_id: int
    charge_id: int
    charge_split_id: int
    posting_date: str
    tax_amount: float
    posttax_amount: float
    pretax_amount: float
    tax_details: List[TaxDetailSchema]
    charge_type: Optional[str] = None
    bill_to_type: Optional[str] = None
    item_id: Optional[str] = None
    sku_category_id: Optional[str] = None


class FolioDetailsRequestSchema(BaseRequestSchema):
    first_name: Optional[str] = None
    is_credit_folio: Optional[bool] = None
    folio_status: Optional[str] = None
    payment_split_details: Optional[List[PaymentSplitDetailSchema]] = None


class FinancialDataSchema(BaseModel):
    hotel_id: str
    date: str
    payment_details: Optional[List[PaymentRequestSchema]] = None
    charge_details: Optional[List[ChargeRequestSchema]] = None
    allowance_details: Optional[List[AllowanceRequestSchema]] = None
    folio_details: Optional[List[FolioDetailsRequestSchema]] = None
