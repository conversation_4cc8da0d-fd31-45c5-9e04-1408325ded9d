from datetime import date, datetime
from typing import Optional, Type

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import isoformat_datetime, ymd_str_to_date

from finance_erp.common.constants import NatureOfSupply, Structure


def date_to_dmy_str(date_object: "date"):
    return date_object.strftime("%d-%m-%Y")


def serialize_with_schema(
    schema: Type[BaseModel], data, many: bool = False, by_alias: bool = True
):
    if many:
        return [
            schema(**obj.model_dump()).model_dump(by_alias=by_alias) for obj in data
        ]
    return schema(**data.model_dump()).model_dump()


class BaseInvoicePushSchema(BaseModel):
    entry_type: Optional[str] = Field(None, alias="EntryType")
    order_date: Optional[date] = Field(None, alias="OrderDate")
    posting_date: Optional[date] = Field(None, alias="PostingDate")
    reference_number: Optional[str] = Field(None, alias="BookingRefNo")
    state_code: Optional[str] = Field(None, alias="State")
    structure: Optional[str] = Field(default=Structure.GST.value, alias="Structure")
    nature_of_supply: Optional[str] = Field(
        default=NatureOfSupply.B2B.value, alias="NatureofSupply"
    )
    unit_price: Optional[float] = Field(None, alias="UnitPrice")
    tax_percentage: Optional[float] = Field(None, alias="GSTGroupCode")
    hotel_name: Optional[str] = Field(None, alias="HotelName")
    check_in: Optional[date] = Field(None, alias="Check-In")
    check_out: Optional[date] = Field(None, alias="Check-Out")
    stay_days: Optional[str] = Field(None, alias="TotalDays")
    room_type: Optional[str] = Field(None, alias="RoomType")
    occupancy: Optional[str] = Field(None, alias="NoofPax")
    guest_name: Optional[str] = Field(None, alias="GuestName")
    uvid_date: Optional[date] = Field(None, alias="UVIDDate")
    invoice_number: Optional[str] = Field(None, alias="UVIDNo")
    total_invoice_amount: Optional[float] = Field(None, alias="UVAmount")
    hotel_code: Optional[str] = Field(None, alias="Hotelcode")
    unique_ref_id: str = Field(..., alias="TransactionRefId")
    source: Optional[str] = Field(None, alias="Source")
    sub_source: Optional[str] = Field(None, alias="SubSource")

    @field_validator("order_date", mode="before")
    def parse_order_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("unit_price", mode="before")
    def parse_unit_price(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("tax_percentage", mode="before")
    def parse_tax_percentage(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("check_in", mode="before")
    def parse_check_in(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("check_out", mode="before")
    def parse_check_out(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    @field_validator("total_invoice_amount", mode="before")
    def parse_total_invoice_amount(cls, value):
        if isinstance(value, str):
            return float(value)
        return value

    @field_validator("uvid_date", mode="before")
    def parse_uvid_date(cls, value):
        if isinstance(value, str):
            return ymd_str_to_date(value)
        return value

    class Config:
        validate_by_name = True
        from_attributes = True


class NavisionResourceCommonResponseSchema(BaseModel):
    status: Optional[str] = None
    verified: Optional[bool] = None
    erp_remarks: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    class Config:
        validate_by_name = True
        from_attributes = True


class BusinessCentralDataPushSchema(BaseModel):
    def dump_with_empty_strings(self, *args, **kwargs):
        data = self.model_dump(*args, **kwargs)
        for field, value in data.items():
            if value is None:
                data[field] = ""
            if isinstance(value, date):
                data[field] = date_to_dmy_str(value)
        return data

    class Config:
        validate_by_name = True
        from_attributes = True
