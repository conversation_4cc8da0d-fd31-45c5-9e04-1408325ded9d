from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class LoanPushSchema(BaseModel):
    hotel_code: str = Field(..., alias="HotelCode")
    posting_date: date = Field(..., alias="PostingDate")
    loan_amount: float = Field(..., alias="LoanAmount")
    remarks: Optional[str] = Field(default=None, alias="Remarks")
    entry_type: str = Field(..., alias="EntryType")
    doc_type: str = Field(..., alias="DocType")
    cost_center_id: Optional[str] = Field(None, alias="CostCenterID")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("loan_amount", mode="before")
    def parse_loan_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class LoanPushRequestSchema(LoanPushSchema, BusinessCentralDataPushSchema):
    ...


class LoanResponseSchema(LoanPushSchema):
    posted_on: Optional[date] = Field(None, alias="PostedOn")
    status: Optional[str] = None
    verified: Optional[bool] = None
    uu_id: Optional[str] = None

    @field_validator("posted_on", mode="before")
    def parse_posted_on(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class LoanUpdateSchema(BaseModel):
    uu_id: str
    verified: Optional[bool] = None
