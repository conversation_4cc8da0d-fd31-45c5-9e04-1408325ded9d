from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class TreeboFeePushSchema(BaseModel):
    hotel_code: str = Field(..., alias="HotelCode")
    settlement_date: date = Field(..., alias="SettlementDate")
    remarks: Optional[str] = Field(default=None, alias="Remarks")
    description: Optional[str] = Field(default=None, alias="Description")
    amount: float = Field(..., alias="UnitAmount")
    gst_percent: Optional[float] = Field(default=None, alias="GSTPercent")
    entry_type: str = Field(..., alias="EntryType")
    hsn_code: str = Field(..., alias="HSNSACCode")
    doc_type: str = Field(..., alias="DocType")
    cost_center_id: Optional[str] = Field(default=None, alias="CostCenterID")

    @field_validator("settlement_date", mode="before")
    def parse_settlement_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("gst_percent", mode="before")
    def parse_gst_percent(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class TreeboFeePushRequestSchema(TreeboFeePushSchema, BusinessCentralDataPushSchema):
    ...


class TreeboFeeResponseSchema(TreeboFeePushSchema):
    posted_on: Optional[date] = Field(default=None, alias="PostedOn")
    status: Optional[str] = None
    verified: Optional[bool] = None
    uu_id: Optional[str] = None

    @field_validator("posted_on", mode="before")
    def parse_posted_on(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class TreeboFeeUpdateSchema(BaseModel):
    uu_id: str
    verified: Optional[bool] = None
