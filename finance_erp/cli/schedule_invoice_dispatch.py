import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.application.corporate.communication.invoice_dispatcher import (
    CorporateInvoiceDispatcher,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.globals import consumer_context
from object_registry import inject


@click.command("schedule_invoice_dispatch")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    "--invoice_ids",
    help="Invoices ids to dispatch",
    required=True,
)
@inject(
    dispatcher=CorporateInvoiceDispatcher,
)
@with_appcontext
def schedule_invoice_dispatch(
    invoice_ids,
    dispatcher: CorporateInvoiceDispatcher,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    click.echo("Stay summary Id: %s" % invoice_ids)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    schedule(dispatcher, invoice_ids.split(","))


@session_manager(commit=True)
def schedule(dispatcher, invoice_ids):
    dispatcher.scheduled_manual_dispatch(invoice_ids)
