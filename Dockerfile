FROM python:3.9-slim-buster

# Create necessary directories
RUN mkdir -p /usr/src/finance_erp \
    /ebs1/logs \
    /var/log/finance_erp \
    /usr/src/scripts \
    /gunicorn-tmp

# Install dependencies
RUN apt-get update \
    && apt-get -y install libpq-dev gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Argument for requirements file
ARG req_file=requirements/deploy.txt

# Set working directory
WORKDIR /usr/src/finance_erp

# Copy requirements and install Python dependencies
COPY requirements /usr/src/finance_erp/requirements
RUN pip3 install --no-cache-dir -r $req_file

# Copy the rest of the application code
COPY . /usr/src/finance_erp/

# Set environment variables
ENV PYTHONPATH="${PYTHONPATH}:/usr/src/finance_erp"
ENV FLASK_APP=finance_erp.wsgi:app

# Expose the application port
EXPOSE 9021
