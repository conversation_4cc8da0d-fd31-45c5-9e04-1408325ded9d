0000000000000000000000000000000000000000 3406c728dd39383db1c4199239e80dad48d0da9b Arvind A <<EMAIL>> 1741247118 +0530	clone: from github.com:treebo-noss/finance_erp.git
3406c728dd39383db1c4199239e80dad48d0da9b 3406c728dd39383db1c4199239e80dad48d0da9b Arvind A <<EMAIL>> 1741343383 +0530	checkout: moving from main to PROM-18245-booker-profile-id-comma-separated
3406c728dd39383db1c4199239e80dad48d0da9b 1414b50ce98f07d258c111e1de880e0ed83ed526 Arvind A <<EMAIL>> 1741347697 +0530	commit: updated schema
1414b50ce98f07d258c111e1de880e0ed83ed526 f67edb0bf43e41cc2ff191bb0ce3ee490a625f31 Arvind A <<EMAIL>> 1741347869 +0530	commit: fixed validation and changed file name
f67edb0bf43e41cc2ff191bb0ce3ee490a625f31 1dfac168a461d9e05bab4efeef16bf570be50f96 Arvind A <<EMAIL>> 1741348035 +0530	commit: updated schema
1dfac168a461d9e05bab4efeef16bf570be50f96 267ec8beabcf55a306c1b6b6723a1092a614175a Arvind A <<EMAIL>> 1741348067 +0530	commit: updated type
267ec8beabcf55a306c1b6b6723a1092a614175a 4093470a433e1d743545a42a070f0a7b3e5e4e16 Arvind A <<EMAIL>> 1741348129 +0530	commit: updated the query for invoices
4093470a433e1d743545a42a070f0a7b3e5e4e16 607a5d34e87502f4b75dbcbd467b9322cc4a237d Arvind A <<EMAIL>> 1741348271 +0530	commit: updated post method body
607a5d34e87502f4b75dbcbd467b9322cc4a237d 47be37cfaa3094355d41ff0662f215042048e194 Arvind A <<EMAIL>> 1741608970 +0530	commit: updated the dto and schema
47be37cfaa3094355d41ff0662f215042048e194 5c527a9dd4f5c449e4a6d6c9efc398e126af822f Arvind A <<EMAIL>> 1741609046 +0530	commit: fixed the variable name
5c527a9dd4f5c449e4a6d6c9efc398e126af822f bc6ca9413b0311b268498c5649652d6433866232 Arvind A <<EMAIL>> 1741609078 +0530	commit: removed tuple from the query
bc6ca9413b0311b268498c5649652d6433866232 a2c344bb93f96f694fd8c56252ba81a6d53bb6df Arvind A <<EMAIL>> 1741609125 +0530	commit: updated the variable name in the post body
a2c344bb93f96f694fd8c56252ba81a6d53bb6df ca39284c690eb1c53f7b4251dd157a3ee0f98193 Arvind A <<EMAIL>> 1741758943 +0530	commit: removed console.log line
ca39284c690eb1c53f7b4251dd157a3ee0f98193 ca39284c690eb1c53f7b4251dd157a3ee0f98193 Arvind A <<EMAIL>> 1742276438 +0530	reset: moving to ca39284c690eb1c53f7b4251dd157a3ee0f98193
ca39284c690eb1c53f7b4251dd157a3ee0f98193 7f6e57ba2f147024f8ce3cb8f7229c4e3090d65c Arvind A <<EMAIL>> 1742281314 +0530	commit: fixed for consumer
7f6e57ba2f147024f8ce3cb8f7229c4e3090d65c c424b2d008922da3a40238dfdce910f4018eafe6 Arvind A <<EMAIL>> 1742281344 +0530	commit: fixed the download invoice
c424b2d008922da3a40238dfdce910f4018eafe6 92715a4054188f54db3e92175ccf1119d4ec5a7d Arvind A <<EMAIL>> 1742286026 +0530	commit: fixed download_invoice function
92715a4054188f54db3e92175ccf1119d4ec5a7d 9c5087486d3f2eb38b364f32ecc59b24a73d9750 Arvind A <<EMAIL>> 1742294041 +0530	commit: updated variable name
9c5087486d3f2eb38b364f32ecc59b24a73d9750 3406c728dd39383db1c4199239e80dad48d0da9b Arvind A <<EMAIL>> 1742973390 +0530	checkout: moving from PROM-18245-booker-profile-id-comma-separated to main
3406c728dd39383db1c4199239e80dad48d0da9b 03e9fd6c8ca268d4d54cbdbb0e0c921918fbc731 Arvind A <<EMAIL>> 1742973408 +0530	pull origin main: Fast-forward
03e9fd6c8ca268d4d54cbdbb0e0c921918fbc731 b4fc0029dc4bf1d08d345e942ec6b0da7fd3f1f6 Arvind A <<EMAIL>> 1743071184 +0530	pull origin main: Fast-forward
b4fc0029dc4bf1d08d345e942ec6b0da7fd3f1f6 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744173552 +0530	pull origin main: Fast-forward
111d7921427d163fd8f456d605d359d664f0bd9d 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744195749 +0530	checkout: moving from main to feat/PROM-18374
111d7921427d163fd8f456d605d359d664f0bd9d 6ee42e74fd032316548c9b28a4540476625b50cf arvind-a-1 <<EMAIL>> 1744196842 +0530	commit: integrate sentry
6ee42e74fd032316548c9b28a4540476625b50cf 9bc110316adbc0c6be3b9d6b85f492001be87268 arvind-a-1 <<EMAIL>> 1744196862 +0530	commit: updated middleware
9bc110316adbc0c6be3b9d6b85f492001be87268 583e7e35bc5cff6d6a4ce44fd0e35421d400f256 arvind-a-1 <<EMAIL>> 1744196919 +0530	commit: updated requirements for sentry
583e7e35bc5cff6d6a4ce44fd0e35421d400f256 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744266932 +0530	checkout: moving from feat/PROM-18374 to main
111d7921427d163fd8f456d605d359d664f0bd9d 583e7e35bc5cff6d6a4ce44fd0e35421d400f256 arvind-a-1 <<EMAIL>> 1744348771 +0530	checkout: moving from main to feat/PROM-18374
583e7e35bc5cff6d6a4ce44fd0e35421d400f256 27b109e4c3f88f826958293433b5d8300f07a057 arvind-a-1 <<EMAIL>> 1744355487 +0530	commit: updated tenant id for sentry
27b109e4c3f88f826958293433b5d8300f07a057 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744355931 +0530	checkout: moving from feat/PROM-18374 to main
111d7921427d163fd8f456d605d359d664f0bd9d 183c68cb50dd1f48734904ed0205b63b0bd31842 arvind-a-1 <<EMAIL>> 1744613774 +0530	checkout: moving from main to develop
183c68cb50dd1f48734904ed0205b63b0bd31842 bf6b317d70f49e7731c52dda196171eef0169aab arvind-a-1 <<EMAIL>> 1744613802 +0530	merge origin/develop: Fast-forward
bf6b317d70f49e7731c52dda196171eef0169aab 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744615350 +0530	checkout: moving from develop to main
111d7921427d163fd8f456d605d359d664f0bd9d 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744615358 +0530	checkout: moving from main to main
111d7921427d163fd8f456d605d359d664f0bd9d 27b109e4c3f88f826958293433b5d8300f07a057 arvind-a-1 <<EMAIL>> 1744615398 +0530	checkout: moving from main to feat/PROM-18374
27b109e4c3f88f826958293433b5d8300f07a057 76bb007df152e93fd1b67aaa6956ca25c6f44139 arvind-a-1 <<EMAIL>> 1744615516 +0530	commit: fx bug
76bb007df152e93fd1b67aaa6956ca25c6f44139 923cf2a101329b3abf1f7a7c31e2335aa54e64d8 arvind-a-1 <<EMAIL>> 1744617459 +0530	pull origin feat/PROM-18374: Fast-forward
923cf2a101329b3abf1f7a7c31e2335aa54e64d8 260d54bfc2f40779070aabc3a7442c4eea1d6593 arvind-a-1 <<EMAIL>> 1744618202 +0530	commit: fix app.py
260d54bfc2f40779070aabc3a7442c4eea1d6593 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744623377 +0530	checkout: moving from feat/PROM-18374 to main
111d7921427d163fd8f456d605d359d664f0bd9d 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1744652722 +0530	checkout: moving from main to fix/PROM-18381
111d7921427d163fd8f456d605d359d664f0bd9d a89f68c865c22b6b2b3a65e051dac00a0d77b806 arvind-a-1 <<EMAIL>> 1744652783 +0530	commit: updated for ota
a89f68c865c22b6b2b3a65e051dac00a0d77b806 e0e4317cf33f63d76584d5e5a88903b642a39598 arvind-a-1 <<EMAIL>> 1744652816 +0530	commit: updated for payment
e0e4317cf33f63d76584d5e5a88903b642a39598 5ba021e1f1da1d9b642747a6940f95e7f346846d arvind-a-1 <<EMAIL>> 1744652849 +0530	commit: updated for reseller
5ba021e1f1da1d9b642747a6940f95e7f346846d 020abec9ab64c62f8a91538dd2d5361608ab1f6a arvind-a-1 <<EMAIL>> 1744652898 +0530	commit: updated for expense
020abec9ab64c62f8a91538dd2d5361608ab1f6a 94603f2c51b4f90514eccd70b09f6ce316216d36 arvind-a-1 <<EMAIL>> 1744652931 +0530	commit: updated for settlement
94603f2c51b4f90514eccd70b09f6ce316216d36 e982cdd3d95c561f61f23b0aa660b4cab89303c2 arvind-a-1 <<EMAIL>> 1744652962 +0530	commit: updated common entity
e982cdd3d95c561f61f23b0aa660b4cab89303c2 90dd9378552b3b8f8bdc6fcd5a33681ef00986dd arvind-a-1 <<EMAIL>> 1744652986 +0530	commit: updated db fields
90dd9378552b3b8f8bdc6fcd5a33681ef00986dd ea17c8980d83579b95481ab0ce2eb1217e6d34ef arvind-a-1 <<EMAIL>> 1744797185 +0530	commit: updated for pydantic parser
ea17c8980d83579b95481ab0ce2eb1217e6d34ef 62d50b6d4d82f5ca85a06f1459f5ab393620a15f arvind-a-1 <<EMAIL>> 1744797215 +0530	commit: updated schema
62d50b6d4d82f5ca85a06f1459f5ab393620a15f 8e2819e6693c3b51b9cde000e6c1cf3aa536910f arvind-a-1 <<EMAIL>> 1744797247 +0530	commit: updated exprense schema
8e2819e6693c3b51b9cde000e6c1cf3aa536910f b61e5540e33c67c32367faa12f5f3caefd2eb0a2 arvind-a-1 <<EMAIL>> 1744797297 +0530	commit: updated common exprense schema
b61e5540e33c67c32367faa12f5f3caefd2eb0a2 4923949449dee76dbf86bea29ba716a2c77c91d1 arvind-a-1 <<EMAIL>> 1744797309 +0530	commit: updated requirements
4923949449dee76dbf86bea29ba716a2c77c91d1 17ba8a97ce9986060fee2783a24ebca32cb23fc6 arvind-a-1 <<EMAIL>> 1745296723 +0530	commit: updated request parser for pydantic
17ba8a97ce9986060fee2783a24ebca32cb23fc6 c6073ebc0a4f40d9f916336cb3279daea6826bbe arvind-a-1 <<EMAIL>> 1745296747 +0530	commit: updated db layer
c6073ebc0a4f40d9f916336cb3279daea6826bbe 47cdf5b9dfe21f5b31e3f3354bd0ab4fd6e83ff7 arvind-a-1 <<EMAIL>> 1745296829 +0530	commit: updated domain layer
47cdf5b9dfe21f5b31e3f3354bd0ab4fd6e83ff7 6542beb892d79651fff204a18dc81bd4afa9e43c arvind-a-1 <<EMAIL>> 1745296986 +0530	commit: updated schema to pydantic
6542beb892d79651fff204a18dc81bd4afa9e43c 2aaa311e3fab0ac1c518e32d9edf5ae09bc437af arvind-a-1 <<EMAIL>> 1745297042 +0530	commit: updated schemas
2aaa311e3fab0ac1c518e32d9edf5ae09bc437af 393ba5f71ea54bcdcf41a61be81a42980c9266a0 arvind-a-1 <<EMAIL>> 1745297074 +0530	commit: updated api for pydantic
393ba5f71ea54bcdcf41a61be81a42980c9266a0 7f906598a3732d57acda1cb7eb7c8291cf03802e arvind-a-1 <<EMAIL>> 1745297102 +0530	commit: updated app.py
7f906598a3732d57acda1cb7eb7c8291cf03802e 19bca4fbca1d42090a94a5d73d01438df5a7d9f2 arvind-a-1 <<EMAIL>> 1745297131 +0530	commit: updated application layer
19bca4fbca1d42090a94a5d73d01438df5a7d9f2 28c939d0e106bbd427d14d0af83f9573ff7a338b arvind-a-1 <<EMAIL>> 1745297187 +0530	commit: updated requirements for pydantic
28c939d0e106bbd427d14d0af83f9573ff7a338b 2dbbd5ac4e8ee68d3621fb9946066d915b43680b arvind-a-1 <<EMAIL>> 1745342254 +0530	commit: updated requests schema
2dbbd5ac4e8ee68d3621fb9946066d915b43680b 56c7468f493d81ed56f0f5348edb6fbc670ac694 arvind-a-1 <<EMAIL>> 1745342296 +0530	commit: updated common schema
56c7468f493d81ed56f0f5348edb6fbc670ac694 b3bddbcd2c3aafb33cf4839e576d7d612b48a58e arvind-a-1 <<EMAIL>> 1745342327 +0530	commit: Updated domain layer
b3bddbcd2c3aafb33cf4839e576d7d612b48a58e 9453b96b7061f82b9e7eea0622da6fbb58aa61c5 arvind-a-1 <<EMAIL>> 1745342357 +0530	commit: updated api layer
9453b96b7061f82b9e7eea0622da6fbb58aa61c5 0c26fe4f067cd03da376a947f42901a239c64357 arvind-a-1 <<EMAIL>> 1745342373 +0530	commit: updated db file
0c26fe4f067cd03da376a947f42901a239c64357 795f29736a95e50146832ffb897bd7655316fa88 arvind-a-1 <<EMAIL>> 1745342447 +0530	commit: updated application layer
795f29736a95e50146832ffb897bd7655316fa88 6c8913f2002d9b5bd9b1ebb4d846f3a64b340776 arvind-a-1 <<EMAIL>> 1745342462 +0530	commit: updated middleware
6c8913f2002d9b5bd9b1ebb4d846f3a64b340776 f3e0a6dd16970fa706d84ee41ae6c21aff79be99 arvind-a-1 <<EMAIL>> 1745342489 +0530	commit: updated test cases
f3e0a6dd16970fa706d84ee41ae6c21aff79be99 fa5256329bc7540d060d75364bf5d26a92c2e175 arvind-a-1 <<EMAIL>> 1745342873 +0530	pull origin fix/PROM-18381: Fast-forward
fa5256329bc7540d060d75364bf5d26a92c2e175 e05525655e984d21bd911572565f3c2eebc426e1 arvind-a-1 <<EMAIL>> 1745343122 +0530	commit: fixed black and isort format
e05525655e984d21bd911572565f3c2eebc426e1 04b517889b0c1e52d311637912d42679f4fdbcd1 arvind-a-1 <<EMAIL>> 1745343460 +0530	commit: updated requirements
04b517889b0c1e52d311637912d42679f4fdbcd1 6776d411864882cdaf17e6e85778257893f58de8 arvind-a-1 <<EMAIL>> 1745411769 +0530	commit: updated api layer
6776d411864882cdaf17e6e85778257893f58de8 46f51508716f43fa4e6d148caa6b3e4321a3f8d8 arvind-a-1 <<EMAIL>> 1745411794 +0530	commit: updated schema
46f51508716f43fa4e6d148caa6b3e4321a3f8d8 f392c1051d318e61d684758734d735b12af42a49 arvind-a-1 <<EMAIL>> 1745411823 +0530	commit: updated pip version to 24.1
f392c1051d318e61d684758734d735b12af42a49 **************************************** arvind-a-1 <<EMAIL>> 1745470492 +0530	commit: fix for sonarqube function length
**************************************** 49171dfe8691f8db8731cb9dfded96d0a7e9de28 arvind-a-1 <<EMAIL>> 1745855938 +0530	commit: updated entities to pydantic
49171dfe8691f8db8731cb9dfded96d0a7e9de28 83ce19ef4bf82f4426a4bc2cebb7b8808cfb2d7a arvind-a-1 <<EMAIL>> 1745855971 +0530	commit: updated api with helper function
83ce19ef4bf82f4426a4bc2cebb7b8808cfb2d7a 7bbf919c304a8812166f9ad8afc4b1ff4f6b9a1e arvind-a-1 <<EMAIL>> 1745855993 +0530	commit: updated application layer
7bbf919c304a8812166f9ad8afc4b1ff4f6b9a1e c287236b2530ed1019a1c98a9112227d0ac37ace arvind-a-1 <<EMAIL>> 1745856011 +0530	commit: updated schema
c287236b2530ed1019a1c98a9112227d0ac37ace 276a2d543d224ef017c91e4e95d91a92779cf14c arvind-a-1 <<EMAIL>> 1745856036 +0530	commit: updated db files
276a2d543d224ef017c91e4e95d91a92779cf14c 1619942a60569ed91b98f69e1adb273ca0b13937 arvind-a-1 <<EMAIL>> 1745924380 +0530	commit: updated schemas and models to pydantic
1619942a60569ed91b98f69e1adb273ca0b13937 1dd015f58d3fd71eb9348136a1abf83ab9157685 arvind-a-1 <<EMAIL>> 1745924419 +0530	commit: removed marshmallow from requirements
1dd015f58d3fd71eb9348136a1abf83ab9157685 1b9ae8402d47c2f2b29641ef4173d72e712dd30a arvind-a-1 <<EMAIL>> 1746446868 +0530	commit: moved all dataclasses to pydantic schemas
1b9ae8402d47c2f2b29641ef4173d72e712dd30a 101c343d8a403da9a669b72761f5ad899965ff56 arvind-a-1 <<EMAIL>> 1746446892 +0530	commit: updated tds_per field
101c343d8a403da9a669b72761f5ad899965ff56 66f217604826737d238efe50fcf6eac56c91e826 arvind-a-1 <<EMAIL>> 1746446932 +0530	commit: updated api layer
66f217604826737d238efe50fcf6eac56c91e826 1ace176cdaa9b2a72fc67a6a1c1749a18a03e221 arvind-a-1 <<EMAIL>> 1746446952 +0530	commit: updated dto and command handler
1ace176cdaa9b2a72fc67a6a1c1749a18a03e221 e15647f0494037f178fef3bac16251d6284e5685 arvind-a-1 <<EMAIL>> 1746446976 +0530	commit: updated job entity schema
e15647f0494037f178fef3bac16251d6284e5685 111d7921427d163fd8f456d605d359d664f0bd9d arvind-a-1 <<EMAIL>> 1746524105 +0530	checkout: moving from fix/PROM-18381 to main
111d7921427d163fd8f456d605d359d664f0bd9d b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f arvind-a-1 <<EMAIL>> 1746524152 +0530	pull origin main: Fast-forward
b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f arvind-a-1 <<EMAIL>> 1747763500 +0530	checkout: moving from main to feat/PROM-18569-invoice-booking-reference-filters-bulk-download
b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f 515eb8e058db7f72f6cd9995737e90d2aa931442 arvind-a-1 <<EMAIL>> 1747763544 +0530	commit: updated schema
515eb8e058db7f72f6cd9995737e90d2aa931442 c98243c16e8e869e6d7627af9659b140ca9f9479 arvind-a-1 <<EMAIL>> 1747763571 +0530	commit: updated application to download invoices
c98243c16e8e869e6d7627af9659b140ca9f9479 798a3c4f7948b6ea37faca28d22713b70a9f1c56 arvind-a-1 <<EMAIL>> 1747763630 +0530	commit: updated job scheduler with updated data
798a3c4f7948b6ea37faca28d22713b70a9f1c56 7c0d680740f8155272961bee284852ff50980c91 arvind-a-1 <<EMAIL>> 1747763651 +0530	commit: updated domain layer
7c0d680740f8155272961bee284852ff50980c91 27fda08cc66a64ed8271416646d21e5a19267e51 arvind-a-1 <<EMAIL>> 1747763673 +0530	commit: updated form and validation in templates
27fda08cc66a64ed8271416646d21e5a19267e51 6f776f9fac4a0bcb6c6424202679a63b564e98c6 arvind-a-1 <<EMAIL>> 1747812748 +0530	commit: updated variable name
6f776f9fac4a0bcb6c6424202679a63b564e98c6 92dc265e9b33e0aa035f94794c60a96502386661 arvind-a-1 <<EMAIL>> 1747816671 +0530	commit: updated variable name
92dc265e9b33e0aa035f94794c60a96502386661 a303e4f0bde84f12b3af5865c47cd44ea6949b17 arvind-a-1 <<EMAIL>> 1748248455 +0530	commit: updated for dates
a303e4f0bde84f12b3af5865c47cd44ea6949b17 e270946b6b1b64696cfd093b8dd80ebf1c384fc9 arvind-a-1 <<EMAIL>> 1748248511 +0530	commit: updated application layer
e270946b6b1b64696cfd093b8dd80ebf1c384fc9 010570fc46d576a8c2ea162784fc95f47d4a659d arvind-a-1 <<EMAIL>> 1748248532 +0530	commit: updated queries to make date optional
010570fc46d576a8c2ea162784fc95f47d4a659d 210f3e39d440641e5e958829c77093b9fa788a40 arvind-a-1 <<EMAIL>> 1748248547 +0530	commit: updated template
210f3e39d440641e5e958829c77093b9fa788a40 304fda24ab49d3e4b3cd575aabe069368b1a0f4c arvind-a-1 <<EMAIL>> 1748263590 +0530	commit: updated queries
304fda24ab49d3e4b3cd575aabe069368b1a0f4c 99d2233a26c57c0e043fc68e08763826ed752386 arvind-a-1 <<EMAIL>> 1748337662 +0530	commit: fixed bug
99d2233a26c57c0e043fc68e08763826ed752386 bbc3f63769ea478f850a84d492ec6e62bc59025f arvind-a-1 <<EMAIL>> 1748342661 +0530	pull: Fast-forward
bbc3f63769ea478f850a84d492ec6e62bc59025f b1c0409852dfd5a1adecaa25e87cba8d8a41671c arvind-a-1 <<EMAIL>> 1748342947 +0530	commit: updated loggers
b1c0409852dfd5a1adecaa25e87cba8d8a41671c b7e80dd716c452e9922b383ad043e2eb409186c4 arvind-a-1 <<EMAIL>> 1748345845 +0530	commit: Added logging
b7e80dd716c452e9922b383ad043e2eb409186c4 9964d505720fc48c7a7de1311dc34c055846dfe0 arvind-a-1 <<EMAIL>> 1748345961 +0530	reset: moving to 9964d50
9964d505720fc48c7a7de1311dc34c055846dfe0 d59a6ce50ab026716bd6bc1d1cc6a0a21bd058b5 arvind-a-1 <<EMAIL>> 1748346108 +0530	commit: updated for logging
d59a6ce50ab026716bd6bc1d1cc6a0a21bd058b5 b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f arvind-a-1 <<EMAIL>> 1748798611 +0530	checkout: moving from feat/PROM-18569-invoice-booking-reference-filters-bulk-download to main
b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f arvind-a-1 <<EMAIL>> 1748803906 +0530	checkout: moving from main to PROM-18569
b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f 69f4ad5b56bebe37bc7ff58d74c813d5145eb015 arvind-a-1 <<EMAIL>> 1748803932 +0530	commit: updated api layer
69f4ad5b56bebe37bc7ff58d74c813d5145eb015 817d26c28a3442b20bf57c08b656e296717f98ce arvind-a-1 <<EMAIL>> 1748803955 +0530	commit: updated application layer
817d26c28a3442b20bf57c08b656e296717f98ce 26c8637e6440ede02156d566214b790b98796283 arvind-a-1 <<EMAIL>> 1748803974 +0530	commit: updated repo layer
26c8637e6440ede02156d566214b790b98796283 c84f6e9734d55deece9f92a42f0a1ee0eb9608a8 arvind-a-1 <<EMAIL>> 1748804006 +0530	commit: updated params in job
c84f6e9734d55deece9f92a42f0a1ee0eb9608a8 5ce5f71484bdd7c637803362409b7435aa8c4b11 arvind-a-1 <<EMAIL>> 1748804021 +0530	commit: updated template
5ce5f71484bdd7c637803362409b7435aa8c4b11 f243383772b2acbdef3b3e786c36c37eed1fa051 arvind-a-1 <<EMAIL>> 1748849030 +0530	commit: updated schema validation error and end_date
f243383772b2acbdef3b3e786c36c37eed1fa051 95bf55075e80f221aba3ee224b073db42061a6e0 arvind-a-1 <<EMAIL>> 1748852478 +0530	commit: fix for end date
95bf55075e80f221aba3ee224b073db42061a6e0 b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f arvind-a-1 <<EMAIL>> 1748946454 +0530	checkout: moving from PROM-18569 to main
b68fb0846bb3d5ee9977b8dfbe1d0f059990ee5f 0a8465eb8b7ade4b665c66291219bfa14775c9c8 arvind-a-1 <<EMAIL>> 1748946489 +0530	pull origin main: Fast-forward
0a8465eb8b7ade4b665c66291219bfa14775c9c8 e15647f0494037f178fef3bac16251d6284e5685 arvind-a-1 <<EMAIL>> 1748947006 +0530	checkout: moving from main to fix/PROM-18381
e15647f0494037f178fef3bac16251d6284e5685 1f8042c4590f0c567781351b5da2d1010dc8ef6b arvind-a-1 <<EMAIL>> 1748947026 +0530	pull: Fast-forward
1f8042c4590f0c567781351b5da2d1010dc8ef6b 90b0ed49484efd8682a6a55536c53dfcc862c3d3 arvind-a-1 <<EMAIL>> 1748950371 +0530	commit: updated schema to pydantic
90b0ed49484efd8682a6a55536c53dfcc862c3d3 35ad8475b3403993498a5c9072b7f46bff5432e3 arvind-a-1 <<EMAIL>> 1748950444 +0530	commit: updated entites
35ad8475b3403993498a5c9072b7f46bff5432e3 d4a616a4bc33201324e8244e14431a538fadbe84 arvind-a-1 <<EMAIL>> 1748950460 +0530	commit: updated for ota
d4a616a4bc33201324e8244e14431a538fadbe84 9f89499d2be597cf52f6f038b6951e229cd4c73a arvind-a-1 <<EMAIL>> 1748950482 +0530	commit: updated tds_percentage for ota
9f89499d2be597cf52f6f038b6951e229cd4c73a 6b1c907a8e10417f29935e013b54d80eeb3d8c60 arvind-a-1 <<EMAIL>> 1748950755 +0530	commit: updated for tds_percentage
6b1c907a8e10417f29935e013b54d80eeb3d8c60 1e2dd4ec39dc8b50d2ed0a042a72d1cfcf4b3b8e arvind-a-1 <<EMAIL>> 1748956519 +0530	commit: updated for tds_percentage
1e2dd4ec39dc8b50d2ed0a042a72d1cfcf4b3b8e 214f7d73fe74c71c7accab60b93c190fa09c2641 arvind-a-1 <<EMAIL>> 1748956552 +0530	commit: updated schema for ta commission
214f7d73fe74c71c7accab60b93c190fa09c2641 d67402d56342f9495b92c8976134fa357746f5f9 arvind-a-1 <<EMAIL>> 1748956569 +0530	commit: updated db file
d67402d56342f9495b92c8976134fa357746f5f9 d67402d56342f9495b92c8976134fa357746f5f9 arvind-a-1 <<EMAIL>> 1749485008 +0530	checkout: moving from fix/PROM-18381 to patch-PROM-18381
d67402d56342f9495b92c8976134fa357746f5f9 9c8f2f83d68c4fa41c76df631689111bc22dc4f9 arvind-a-1 <<EMAIL>> 1749485211 +0530	commit: updated for reference number
9c8f2f83d68c4fa41c76df631689111bc22dc4f9 53dfe11f58109c325a2b71a6b103858f2d1e0d5b arvind-a-1 <<EMAIL>> 1749485239 +0530	commit: updaetd entities and schema
53dfe11f58109c325a2b71a6b103858f2d1e0d5b 3324a048e59844c746baa56bcce154732e8e1c5a arvind-a-1 <<EMAIL>> 1749485280 +0530	commit: updated common schemas
3324a048e59844c746baa56bcce154732e8e1c5a d898f3b5440379d48417ebcbace3bfb0104f241b arvind-a-1 <<EMAIL>> 1749659917 +0530	commit: fixed invoice download
d898f3b5440379d48417ebcbace3bfb0104f241b eebef023f212a5835871a0e669f919a0674142b6 arvind-a-1 <<EMAIL>> 1749709690 +0530	commit: updated schema for legal file response
eebef023f212a5835871a0e669f919a0674142b6 09c4a85aa56d48593ec72b2dc241124f8d5997a3 arvind-a-1 <<EMAIL>> 1749710484 +0530	commit: updated attachment
09c4a85aa56d48593ec72b2dc241124f8d5997a3 0d7d9a4a564dcfdc71583b50ffc6d5823c983710 arvind-a-1 <<EMAIL>> 1749750063 +0530	commit: updated domain layer
0d7d9a4a564dcfdc71583b50ffc6d5823c983710 f8764232065b7d9aa30c62114f74a627c5c7bdd6 arvind-a-1 <<EMAIL>> 1749750093 +0530	commit: removed print statements
f8764232065b7d9aa30c62114f74a627c5c7bdd6 4fc79587b794c66a554f2d1a7e897173a3fb9680 arvind-a-1 <<EMAIL>> 1749750177 +0530	reset: moving to 4fc79587b794c66a554f2d1a7e897173a3fb9680
4fc79587b794c66a554f2d1a7e897173a3fb9680 475ccc802a575e8c44b250a4eb7c3555fcce781a arvind-a-1 <<EMAIL>> 1749751101 +0530	commit: updated domain layer
475ccc802a575e8c44b250a4eb7c3555fcce781a 0269831e402a3fb864c3fd1039590956c6af42c9 arvind-a-1 <<EMAIL>> 1749751113 +0530	commit: updated api layer
0269831e402a3fb864c3fd1039590956c6af42c9 ed05b923f6de139fc0a4e19af042404dc0601db4 arvind-a-1 <<EMAIL>> 1749800004 +0530	commit: updated payments schema
ed05b923f6de139fc0a4e19af042404dc0601db4 236817fd4be39f78b3d82c29690108ce3e2acd29 arvind-a-1 <<EMAIL>> 1749807018 +0530	commit: made email optional
236817fd4be39f78b3d82c29690108ce3e2acd29 977f1bbbab9faf2996fd2f1b4b48324ff268df28 arvind-a-1 <<EMAIL>> 1749813404 +0530	commit: updated exception message
977f1bbbab9faf2996fd2f1b4b48324ff268df28 8efb97351f1811ab5a284b4c535fc8b469dc15b8 arvind-a-1 <<EMAIL>> 1749813436 +0530	commit: updated domain schemas
8efb97351f1811ab5a284b4c535fc8b469dc15b8 4ebd684056031c050362d745d5be88be1e8cdafd arvind-a-1 <<EMAIL>> 1749817290 +0530	commit: updated schemas
4ebd684056031c050362d745d5be88be1e8cdafd 62284d55ed9ea9e304cfa632d9b473987b3747c9 arvind-a-1 <<EMAIL>> 1750054007 +0530	commit: updated entities in domain layer
62284d55ed9ea9e304cfa632d9b473987b3747c9 e1836a64c088658a9961d05c1200d58b552f8870 arvind-a-1 <<EMAIL>> 1750054036 +0530	commit: updated hotel pull report
e1836a64c088658a9961d05c1200d58b552f8870 4916b6195c8f5b17bcec9ecd4ca4f6af0165d24e arvind-a-1 <<EMAIL>> 1750054182 +0530	commit: updated sales entity
4916b6195c8f5b17bcec9ecd4ca4f6af0165d24e 6ef59e3d493fd7d69ce28a1e36eba58547159d05 arvind-a-1 <<EMAIL>> 1750673443 +0530	commit: updated schemas
6ef59e3d493fd7d69ce28a1e36eba58547159d05 c662093ef3c74e41f9cb809f39ff40487bddd1ef arvind-a-1 <<EMAIL>> 1750673466 +0530	commit: updated corporate schemas
c662093ef3c74e41f9cb809f39ff40487bddd1ef f3f2b1e9dd49ad515dd83bc9930138c00135e9a5 arvind-a-1 <<EMAIL>> 1750695889 +0530	commit: updated domain entities
f3f2b1e9dd49ad515dd83bc9930138c00135e9a5 719bd00521c101607d805fd9c3dbbf099ca5d99c arvind-a-1 <<EMAIL>> 1750695909 +0530	commit: updated schemas
719bd00521c101607d805fd9c3dbbf099ca5d99c 67a2d4943b74e2330760786cf251197af7ec8f0d arvind-a-1 <<EMAIL>> 1750745908 +0530	commit: removed monkey patch
67a2d4943b74e2330760786cf251197af7ec8f0d 52523a73ffdb9b9a65bda67ff1dd12a8280e2060 arvind-a-1 <<EMAIL>> 1750745933 +0530	commit: updated schemas
52523a73ffdb9b9a65bda67ff1dd12a8280e2060 ca7429529d054ffbceab21aa9b745b869e290fb1 arvind-a-1 <<EMAIL>> 1750752797 +0530	commit: updated domain layers
ca7429529d054ffbceab21aa9b745b869e290fb1 ca7429529d054ffbceab21aa9b745b869e290fb1 arvind-a-1 <<EMAIL>> 1750772431 +0530	reset: moving to HEAD
ca7429529d054ffbceab21aa9b745b869e290fb1 0a8465eb8b7ade4b665c66291219bfa14775c9c8 arvind-a-1 <<EMAIL>> 1750772438 +0530	checkout: moving from patch-PROM-18381 to main
0a8465eb8b7ade4b665c66291219bfa14775c9c8 ca7429529d054ffbceab21aa9b745b869e290fb1 arvind-a-1 <<EMAIL>> 1750772806 +0530	checkout: moving from main to patch-PROM-18381
ca7429529d054ffbceab21aa9b745b869e290fb1 4344ce7fd2ede2d1bd7a7dac53a54d897010a857 arvind-a-1 <<EMAIL>> 1750786083 +0530	commit: updated schema
4344ce7fd2ede2d1bd7a7dac53a54d897010a857 1330cb73083a57d8438a23283ae385629ad2b197 arvind-a-1 <<EMAIL>> 1750829866 +0530	commit: updated entities
